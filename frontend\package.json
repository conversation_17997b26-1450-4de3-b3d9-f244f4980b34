{"name": "family-oa-platform-frontend", "version": "1.0.0", "description": "Frontend for Family OA Platform - Vue.js application", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"vue": "^3.3.8", "vue-router": "^4.2.5", "pinia": "^2.1.7", "axios": "^1.6.2", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.2", "vite": "^5.0.8", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "@vue/eslint-config-prettier": "^8.0.0", "prettier": "^3.0.3"}}