<template>
  <div class="dashboard">
    <!-- Welcome Section -->
    <div class="welcome-section">
      <el-card class="welcome-card">
        <div class="welcome-content">
          <div class="welcome-text">
            <h1>欢迎回来，{{ authStore.user?.real_name }}！</h1>
            <p>今天是 {{ currentDate }}，让我们一起让家庭沟通更高效。</p>
          </div>
          <div class="welcome-actions">
            <el-button type="primary" size="large" @click="$router.push('/proposals/create')">
              <el-icon><Plus /></el-icon>
              创建新提案
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- Statistics Cards -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon pending">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="stat-info">
                <h3>{{ stats.pending || 0 }}</h3>
                <p>待处理提案</p>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon approved">
                <el-icon><Select /></el-icon>
              </div>
              <div class="stat-info">
                <h3>{{ stats.approved || 0 }}</h3>
                <p>已通过提案</p>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon total">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stat-info">
                <h3>{{ stats.total || 0 }}</h3>
                <p>总提案数</p>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon completed">
                <el-icon><CircleCheck /></el-icon>
              </div>
              <div class="stat-info">
                <h3>{{ stats.completed || 0 }}</h3>
                <p>已完成提案</p>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- Recent Proposals -->
    <div class="recent-section">
      <el-card>
        <template #header>
          <div class="section-header">
            <h2>最近的提案</h2>
            <el-button type="primary" text @click="$router.push('/proposals')">
              查看全部
              <el-icon><ArrowRight /></el-icon>
            </el-button>
          </div>
        </template>
        
        <div v-if="proposalStore.loading" class="loading-container">
          <el-skeleton :rows="3" animated />
        </div>
        
        <div v-else-if="recentProposals.length === 0" class="empty-state">
          <el-empty description="暂无提案">
            <el-button type="primary" @click="$router.push('/proposals/create')">
              创建第一个提案
            </el-button>
          </el-empty>
        </div>
        
        <div v-else class="proposals-list">
          <div
            v-for="proposal in recentProposals"
            :key="proposal.id"
            class="proposal-item"
            @click="$router.push(`/proposals/${proposal.id}`)"
          >
            <div class="proposal-info">
              <h4>{{ proposal.title }}</h4>
              <p class="proposal-description">{{ proposal.description }}</p>
              <div class="proposal-meta">
                <span class="creator">{{ proposal.creator_name }}</span>
                <span class="date">{{ formatDate(proposal.created_at) }}</span>
              </div>
            </div>
            <div class="proposal-status">
              <el-tag :type="getStatusTagType(proposal.status)" class="status-tag">
                {{ getStatusText(proposal.status) }}
              </el-tag>
              <div class="proposal-stats">
                <span><el-icon><ChatDotRound /></el-icon> {{ proposal.comment_count || 0 }}</span>
                <span><el-icon><Select /></el-icon> {{ (proposal.approve_votes || 0) + (proposal.reject_votes || 0) }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { 
  Plus, 
  Clock, 
  Select, 
  Document, 
  CircleCheck, 
  ArrowRight,
  ChatDotRound
} from '@element-plus/icons-vue'
import { useAuthStore } from '../stores/auth'
import { useProposalStore } from '../stores/proposal'

const authStore = useAuthStore()
const proposalStore = useProposalStore()

const stats = ref({
  total: 0,
  pending: 0,
  approved: 0,
  completed: 0
})

const currentDate = computed(() => {
  return new Date().toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  })
})

const recentProposals = computed(() => {
  return proposalStore.proposals.slice(0, 5)
})

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const getStatusText = (status) => {
  const statusMap = {
    draft: '草稿',
    pending: '待处理',
    approved: '已通过',
    rejected: '已拒绝',
    completed: '已完成'
  }
  return statusMap[status] || status
}

const getStatusTagType = (status) => {
  const typeMap = {
    draft: 'info',
    pending: 'warning',
    approved: 'success',
    rejected: 'danger',
    completed: 'success'
  }
  return typeMap[status] || 'info'
}

const loadDashboardData = async () => {
  // Load recent proposals
  await proposalStore.fetchProposals({ limit: 5 })
  
  // Calculate statistics
  const proposals = proposalStore.proposals
  stats.value = {
    total: proposals.length,
    pending: proposals.filter(p => p.status === 'pending').length,
    approved: proposals.filter(p => p.status === 'approved').length,
    completed: proposals.filter(p => p.status === 'completed').length
  }
}

onMounted(() => {
  loadDashboardData()
})
</script>

<style scoped>
.dashboard {
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-section {
  margin-bottom: 24px;
}

.welcome-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}

.welcome-card :deep(.el-card__body) {
  padding: 32px;
}

.welcome-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-text h1 {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 8px;
}

.welcome-text p {
  font-size: 16px;
  opacity: 0.9;
}

.stats-section {
  margin-bottom: 32px;
}

.stat-card {
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
}

.stat-icon.pending {
  background: linear-gradient(135deg, #f6ad55 0%, #ed8936 100%);
}

.stat-icon.approved {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.completed {
  background: linear-gradient(135deg, #4fd1c7 0%, #319795 100%);
}

.stat-info h3 {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 4px;
  color: #2d3748;
}

.stat-info p {
  color: #718096;
  font-size: 14px;
}

.recent-section {
  margin-bottom: 32px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-header h2 {
  font-size: 20px;
  font-weight: 600;
  color: #2d3748;
}

.loading-container {
  padding: 20px;
}

.empty-state {
  padding: 40px 20px;
  text-align: center;
}

.proposals-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.proposal-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.proposal-item:hover {
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
}

.proposal-info {
  flex: 1;
}

.proposal-info h4 {
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 8px;
}

.proposal-description {
  color: #718096;
  font-size: 14px;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  line-height: 1.4;
  max-height: 2.8em;
}

.proposal-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #a0aec0;
}

.proposal-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

.proposal-stats {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #718096;
}

.proposal-stats span {
  display: flex;
  align-items: center;
  gap: 4px;
}

@media (max-width: 768px) {
  .welcome-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .welcome-text h1 {
    font-size: 24px;
  }
  
  .proposal-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .proposal-status {
    width: 100%;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
