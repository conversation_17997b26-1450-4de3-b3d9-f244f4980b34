const express = require('express');
const { pool } = require('../config/database');
const { verifyToken } = require('./auth');
const {
  validateProposalCreation,
  validateProposalUpdate,
  validateCommentCreation,
  validateVote,
  validateId,
  validatePagination
} = require('../middleware/validation');
const { catchAsync } = require('../middleware/errorHandler');
const router = express.Router();

// Get all proposals
router.get('/', verifyToken, validatePagination, catchAsync(async (req, res) => {
  try {
    const { status, page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;

    let query = `
      SELECT p.*,
             u.real_name as creator_name,
             assigned_user.real_name as assigned_name
      FROM proposals p
      LEFT JOIN users u ON p.creator_id = u.id
      LEFT JOIN users assigned_user ON p.assigned_to = assigned_user.id
    `;

    const params = [];
    if (status) {
      query += ' WHERE p.status = ?';
      params.push(status);
    }    // Sanitize limit and offset to prevent SQL injection
    const safeLimit = Math.max(1, Math.min(100, parseInt(limit) || 10)); // Max 100 items per page
    const safeOffset = Math.max(0, parseInt(offset) || 0);
    
    query += ` ORDER BY p.created_at DESC LIMIT ${safeLimit} OFFSET ${safeOffset}`;

    const [proposals] = await pool.execute(query, params);

    // Get comment and vote statistics for each proposal
    if (proposals.length > 0) {
      const proposalIds = proposals.map(p => p.id);
      const placeholders = proposalIds.map(() => '?').join(',');
      
      // Get comment counts
      const [commentStats] = await pool.execute(`
        SELECT proposal_id, COUNT(*) as comment_count
        FROM comments
        WHERE proposal_id IN (${placeholders})
        GROUP BY proposal_id
      `, proposalIds);
      
      // Get vote counts
      const [voteStats] = await pool.execute(`
        SELECT proposal_id,
               COUNT(CASE WHEN vote_type = 'approve' THEN 1 END) as approve_votes,
               COUNT(CASE WHEN vote_type = 'reject' THEN 1 END) as reject_votes
        FROM votes
        WHERE proposal_id IN (${placeholders})
        GROUP BY proposal_id
      `, proposalIds);
      
      // Merge statistics with proposals
      proposals.forEach(proposal => {
        const commentStat = commentStats.find(c => c.proposal_id === proposal.id);
        const voteStat = voteStats.find(v => v.proposal_id === proposal.id);
        
        proposal.comment_count = commentStat ? commentStat.comment_count : 0;
        proposal.approve_votes = voteStat ? voteStat.approve_votes : 0;
        proposal.reject_votes = voteStat ? voteStat.reject_votes : 0;
      });
    }

    // Get total count
    let countQuery = 'SELECT COUNT(DISTINCT p.id) as total FROM proposals p';
    if (status) {
      countQuery += ' WHERE p.status = ?';
    }
    const [countResult] = await pool.execute(countQuery, status ? [status] : []);    res.json({
      proposals,
      total: countResult[0].total,
      page: parseInt(page),
      limit: safeLimit,
      hasMore: countResult[0].total > safeOffset + proposals.length
    });
  } catch (error) {
    console.error('获取提案列表错误:', error);
    res.status(500).json({ error: '获取提案列表失败' });
  }
}));

// Get single proposal
router.get('/:id', verifyToken, validateId, catchAsync(async (req, res) => {
  try {
    const { id } = req.params;

    const [proposals] = await pool.execute(`
      SELECT p.*, 
             u.real_name as creator_name,
             assigned_user.real_name as assigned_name
      FROM proposals p
      LEFT JOIN users u ON p.creator_id = u.id
      LEFT JOIN users assigned_user ON p.assigned_to = assigned_user.id
      WHERE p.id = ?
    `, [id]);

    if (proposals.length === 0) {
      return res.status(404).json({ error: '提案不存在' });
    }

    // Get comments
    const [comments] = await pool.execute(`
      SELECT c.*, u.real_name as user_name, u.avatar_url
      FROM comments c
      LEFT JOIN users u ON c.user_id = u.id
      WHERE c.proposal_id = ?
      ORDER BY c.created_at ASC
    `, [id]);

    // Get votes
    const [votes] = await pool.execute(`
      SELECT v.*, u.real_name as user_name
      FROM votes v
      LEFT JOIN users u ON v.user_id = u.id
      WHERE v.proposal_id = ?
    `, [id]);

    res.json({
      proposal: proposals[0],
      comments,
      votes
    });
  } catch (error) {
    console.error('获取提案详情错误:', error);
    res.status(500).json({ error: '获取提案详情失败' });
  }
}));

// Create new proposal
router.post('/', verifyToken, validateProposalCreation, catchAsync(async (req, res) => {
  try {
    const { title, description, solution, priority = 'medium', dueDate } = req.body;

    if (!title || !description) {
      return res.status(400).json({ error: '标题和描述都是必填的' });
    }

    const [result] = await pool.execute(`
      INSERT INTO proposals (title, description, solution, priority, creator_id, due_date, status)
      VALUES (?, ?, ?, ?, ?, ?, 'draft')
    `, [title, description, solution || null, priority, req.user.userId, dueDate || null]);

    res.status(201).json({
      message: '提案创建成功',
      proposalId: result.insertId
    });
  } catch (error) {
    console.error('创建提案错误:', error);
    res.status(500).json({ error: '创建提案失败' });
  }
}));

// Update proposal
router.put('/:id', verifyToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { title, description, solution, status, priority, assignedTo, dueDate } = req.body;

    // Check if user owns the proposal or is admin
    const [proposals] = await pool.execute(
      'SELECT creator_id FROM proposals WHERE id = ?',
      [id]
    );

    if (proposals.length === 0) {
      return res.status(404).json({ error: '提案不存在' });
    }

    const [users] = await pool.execute(
      'SELECT role FROM users WHERE id = ?',
      [req.user.userId]
    );

    if (proposals[0].creator_id !== req.user.userId && users[0].role !== 'admin') {
      return res.status(403).json({ error: '无权限修改此提案' });
    }

    // Build update query dynamically
    const updates = [];
    const params = [];

    if (title !== undefined) {
      updates.push('title = ?');
      params.push(title);
    }
    if (description !== undefined) {
      updates.push('description = ?');
      params.push(description);
    }
    if (solution !== undefined) {
      updates.push('solution = ?');
      params.push(solution);
    }
    if (status !== undefined) {
      updates.push('status = ?');
      params.push(status);
    }
    if (priority !== undefined) {
      updates.push('priority = ?');
      params.push(priority);
    }
    if (assignedTo !== undefined) {
      updates.push('assigned_to = ?');
      params.push(assignedTo);
    }
    if (dueDate !== undefined) {
      updates.push('due_date = ?');
      params.push(dueDate);
    }

    if (updates.length === 0) {
      return res.status(400).json({ error: '没有提供要更新的字段' });
    }

    params.push(id);
    await pool.execute(
      `UPDATE proposals SET ${updates.join(', ')} WHERE id = ?`,
      params
    );

    res.json({ message: '提案更新成功' });
  } catch (error) {
    console.error('更新提案错误:', error);
    res.status(500).json({ error: '更新提案失败' });
  }
});

// Delete proposal
router.delete('/:id', verifyToken, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if user owns the proposal or is admin
    const [proposals] = await pool.execute(
      'SELECT creator_id FROM proposals WHERE id = ?',
      [id]
    );

    if (proposals.length === 0) {
      return res.status(404).json({ error: '提案不存在' });
    }

    const [users] = await pool.execute(
      'SELECT role FROM users WHERE id = ?',
      [req.user.userId]
    );

    if (proposals[0].creator_id !== req.user.userId && users[0].role !== 'admin') {
      return res.status(403).json({ error: '无权限删除此提案' });
    }

    await pool.execute('DELETE FROM proposals WHERE id = ?', [id]);

    res.json({ message: '提案删除成功' });
  } catch (error) {
    console.error('删除提案错误:', error);
    res.status(500).json({ error: '删除提案失败' });
  }
});

// Add comment to proposal
router.post('/:id/comments', verifyToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { content, commentType = 'neutral' } = req.body;

    if (!content) {
      return res.status(400).json({ error: '评论内容不能为空' });
    }

    // Check if proposal exists
    const [proposals] = await pool.execute(
      'SELECT id FROM proposals WHERE id = ?',
      [id]
    );

    if (proposals.length === 0) {
      return res.status(404).json({ error: '提案不存在' });
    }

    const [result] = await pool.execute(`
      INSERT INTO comments (proposal_id, user_id, content, comment_type)
      VALUES (?, ?, ?, ?)
    `, [id, req.user.userId, content, commentType]);

    res.status(201).json({
      message: '评论添加成功',
      commentId: result.insertId
    });
  } catch (error) {
    console.error('添加评论错误:', error);
    res.status(500).json({ error: '添加评论失败' });
  }
});

// Vote on proposal
router.post('/:id/votes', verifyToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { voteType } = req.body;

    if (!['approve', 'reject', 'abstain'].includes(voteType)) {
      return res.status(400).json({ error: '无效的投票类型' });
    }

    // Check if proposal exists
    const [proposals] = await pool.execute(
      'SELECT id FROM proposals WHERE id = ?',
      [id]
    );

    if (proposals.length === 0) {
      return res.status(404).json({ error: '提案不存在' });
    }

    // Insert or update vote
    await pool.execute(`
      INSERT INTO votes (proposal_id, user_id, vote_type)
      VALUES (?, ?, ?)
      ON DUPLICATE KEY UPDATE vote_type = VALUES(vote_type)
    `, [id, req.user.userId, voteType]);

    res.json({ message: '投票成功' });
  } catch (error) {
    console.error('投票错误:', error);
    res.status(500).json({ error: '投票失败' });
  }
});

module.exports = router;
