@echo off
chcp 65001 >nul
title FamilyOAPlatform 配置向导

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                🔧 FamilyOAPlatform 配置向导                    ║
echo ║                                                              ║
echo ║  本向导将帮助您配置：                                           ║
echo ║  • Google AI API密钥 (必需)                                   ║
echo ║  • 服务器端口设置 (可选)                                       ║
echo ║  • 数据库连接参数 (可选)                                       ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:: 检查配置文件是否存在
if not exist "backend\.env" (
    echo ❌ 错误：配置文件不存在
    echo.
    echo 请先运行 install.bat 完成安装
    echo.
    pause
    exit /b 1
)

:: 创建配置备份
copy "backend\.env" "backend\.env.backup" >nul 2>&1
echo ✅ 已创建配置文件备份

echo.
echo ═══════════════════════════════════════════════════════════════
echo                      Google AI API 配置
echo ═══════════════════════════════════════════════════════════════
echo.

echo 🤖 Google AI API密钥用于启用以下功能：
echo   • 提案内容智能扩展
echo   • 解决方案自动完善
echo   • AI中立仲裁评估
echo   • 评论内容优化建议
echo.

echo 📋 获取API密钥步骤：
echo   1. 访问 https://makersuite.google.com/app/apikey
echo   2. 使用Google账户登录
echo   3. 点击 "Create API Key" 按钮
echo   4. 选择项目或创建新项目
echo   5. 复制生成的API密钥
echo.

echo 💡 提示：API密钥格式类似 "AIzaSyC..." 开头，长度约39个字符
echo.

:: 检查当前API密钥配置
for /f "tokens=2 delims==" %%a in ('findstr "GOOGLE_AI_API_KEY" backend\.env') do set CURRENT_API_KEY=%%a

if "%CURRENT_API_KEY%"=="your_google_ai_api_key_here" (
    echo 📌 当前状态：未配置API密钥
    set API_CONFIGURED=false
) else (
    echo 📌 当前状态：已配置API密钥 (%CURRENT_API_KEY:~0,10%...)
    set API_CONFIGURED=true
)

echo.
if "%API_CONFIGURED%"=="true" (
    echo 是否要更新现有的API密钥？(Y/N)
    set /p update_api="请选择: "
    if /i not "%update_api%"=="Y" (
        echo 保持现有API密钥配置
        goto :port_config
    )
)

:input_api_key
set /p api_key="请输入您的Google AI API密钥: "

:: 验证API密钥格式
if "%api_key%"=="" (
    echo ❌ API密钥不能为空，请重新输入
    echo.
    goto :input_api_key
)

:: 检查API密钥格式
echo %api_key% | findstr /R "^AIza[A-Za-z0-9_-]*$" >nul
if %errorLevel% neq 0 (
    echo ⚠️  警告：API密钥格式可能不正确
    echo 正确格式应以 "AIza" 开头，包含字母、数字、下划线和连字符
    echo.
    echo 是否仍要使用此密钥？(Y/N)
    set /p use_anyway="请选择: "
    if /i not "%use_anyway%"=="Y" (
        goto :input_api_key
    )
)

:: 测试API密钥有效性
echo.
echo 🔍 正在验证API密钥有效性...

:: 使用PowerShell测试API
powershell -Command "try { $response = Invoke-RestMethod -Uri 'https://generativelanguage.googleapis.com/v1/models?key=%api_key%' -Method Get -TimeoutSec 10; if ($response.models) { exit 0 } else { exit 1 } } catch { exit 1 }" >nul 2>&1

if %errorLevel% equ 0 (
    echo ✅ API密钥验证成功
    set API_VALID=true
) else (
    echo ⚠️  API密钥验证失败，可能的原因：
    echo   • API密钥无效或已过期
    echo   • 网络连接问题
    echo   • Google AI服务暂时不可用
    echo.
    echo 是否仍要保存此API密钥？(Y/N)
    set /p save_anyway="请选择: "
    if /i not "%save_anyway%"=="Y" (
        goto :input_api_key
    )
    set API_VALID=false
)

:: 更新配置文件中的API密钥
echo 正在更新配置文件...
powershell -Command "(Get-Content 'backend\.env') -replace 'GOOGLE_AI_API_KEY=.*', 'GOOGLE_AI_API_KEY=%api_key%' | Set-Content 'backend\.env'"

echo ✅ Google AI API密钥配置完成
echo.

:port_config
echo ═══════════════════════════════════════════════════════════════
echo                        端口配置 (可选)
echo ═══════════════════════════════════════════════════════════════
echo.

:: 获取当前端口配置
for /f "tokens=2 delims==" %%a in ('findstr "PORT=" backend\.env') do set CURRENT_PORT=%%a

echo 📌 当前后端端口：%CURRENT_PORT%
echo 📌 当前前端端口：5173 (在 frontend\vite.config.js 中配置)
echo.

echo 是否要修改端口配置？(Y/N)
set /p change_ports="请选择: "

if /i "%change_ports%"=="Y" (
    echo.
    echo 请输入新的后端端口 (当前: %CURRENT_PORT%, 推荐: 3000-9999):
    set /p new_port="后端端口: "
    
    :: 验证端口号
    if "%new_port%"=="" set new_port=%CURRENT_PORT%
    
    :: 检查端口是否被占用
    netstat -an | find ":%new_port%" >nul
    if %errorLevel% equ 0 (
        echo ⚠️  警告：端口 %new_port% 已被占用
        echo 建议选择其他端口或在启动时处理端口冲突
    )
    
    :: 更新后端端口配置
    powershell -Command "(Get-Content 'backend\.env') -replace 'PORT=.*', 'PORT=%new_port%' | Set-Content 'backend\.env'"
    
    :: 更新前端代理配置
    powershell -Command "(Get-Content 'frontend\.env.development') -replace 'VITE_API_BASE_URL=.*', 'VITE_API_BASE_URL=http://localhost:%new_port%/api' | Set-Content 'frontend\.env.development'"
    
    echo ✅ 端口配置已更新
    echo   后端端口: %new_port%
    echo   前端端口: 5173
) else (
    echo 保持当前端口配置
)

echo.
echo ═══════════════════════════════════════════════════════════════
echo                      数据库配置 (可选)
echo ═══════════════════════════════════════════════════════════════
echo.

:: 获取当前数据库配置
for /f "tokens=2 delims==" %%a in ('findstr "DB_HOST=" backend\.env') do set CURRENT_DB_HOST=%%a
for /f "tokens=2 delims==" %%a in ('findstr "DB_PORT=" backend\.env') do set CURRENT_DB_PORT=%%a
for /f "tokens=2 delims==" %%a in ('findstr "DB_NAME=" backend\.env') do set CURRENT_DB_NAME=%%a
for /f "tokens=2 delims==" %%a in ('findstr "DB_USER=" backend\.env') do set CURRENT_DB_USER=%%a

echo 📌 当前数据库配置：
echo   主机: %CURRENT_DB_HOST%
echo   端口: %CURRENT_DB_PORT%
echo   数据库: %CURRENT_DB_NAME%
echo   用户: %CURRENT_DB_USER%
echo.

echo 是否要修改数据库配置？(Y/N)
set /p change_db="请选择: "

if /i "%change_db%"=="Y" (
    echo.
    echo 请输入新的数据库配置 (直接回车保持当前值):
    
    set /p new_db_host="数据库主机 [%CURRENT_DB_HOST%]: "
    if "%new_db_host%"=="" set new_db_host=%CURRENT_DB_HOST%
    
    set /p new_db_port="数据库端口 [%CURRENT_DB_PORT%]: "
    if "%new_db_port%"=="" set new_db_port=%CURRENT_DB_PORT%
    
    set /p new_db_name="数据库名称 [%CURRENT_DB_NAME%]: "
    if "%new_db_name%"=="" set new_db_name=%CURRENT_DB_NAME%
    
    set /p new_db_user="数据库用户 [%CURRENT_DB_USER%]: "
    if "%new_db_user%"=="" set new_db_user=%CURRENT_DB_USER%
    
    set /p new_db_pass="数据库密码 (输入新密码或回车保持不变): "
    
    :: 更新数据库配置
    powershell -Command "(Get-Content 'backend\.env') -replace 'DB_HOST=.*', 'DB_HOST=%new_db_host%' | Set-Content 'backend\.env'"
    powershell -Command "(Get-Content 'backend\.env') -replace 'DB_PORT=.*', 'DB_PORT=%new_db_port%' | Set-Content 'backend\.env'"
    powershell -Command "(Get-Content 'backend\.env') -replace 'DB_NAME=.*', 'DB_NAME=%new_db_name%' | Set-Content 'backend\.env'"
    powershell -Command "(Get-Content 'backend\.env') -replace 'DB_USER=.*', 'DB_USER=%new_db_user%' | Set-Content 'backend\.env'"
    
    if not "%new_db_pass%"=="" (
        powershell -Command "(Get-Content 'backend\.env') -replace 'DB_PASSWORD=.*', 'DB_PASSWORD=%new_db_pass%' | Set-Content 'backend\.env'"
    )
    
    echo ✅ 数据库配置已更新
) else (
    echo 保持当前数据库配置
)

echo.
echo ═══════════════════════════════════════════════════════════════
echo                        配置完成
echo ═══════════════════════════════════════════════════════════════
echo.

:: 显示配置摘要
echo 📋 配置摘要：
echo.

:: 显示API配置状态
if "%API_VALID%"=="true" (
    echo ✅ Google AI API: 已配置并验证
) else if "%API_CONFIGURED%"=="true" (
    echo ⚠️  Google AI API: 已配置但未验证
) else (
    echo ❌ Google AI API: 未配置
)

:: 显示端口配置
for /f "tokens=2 delims==" %%a in ('findstr "PORT=" backend\.env') do echo ✅ 后端端口: %%a
echo ✅ 前端端口: 5173

:: 显示数据库配置
echo ✅ 数据库: %CURRENT_DB_HOST%:%CURRENT_DB_PORT%/%CURRENT_DB_NAME%

echo.
echo 💾 配置文件已保存到: backend\.env
echo 💾 备份文件位置: backend\.env.backup
echo.

:: 测试数据库连接
echo 🔍 正在测试数据库连接...
if exist "mysql\bin\mysql.exe" (
    mysql\bin\mysql -u %CURRENT_DB_USER% -pFamilyOA@2024 -e "SELECT 1;" >nul 2>&1
    if %errorLevel% equ 0 (
        echo ✅ 数据库连接测试成功
    ) else (
        echo ⚠️  数据库连接测试失败，请检查配置
    )
) else (
    echo ⚠️  未找到本地MySQL，跳过连接测试
)

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🎉 配置完成！                              ║
echo ║                                                              ║
echo ║  下一步操作：                                                  ║
echo ║  1. 运行 start.bat 启动服务                                    ║
echo ║  2. 浏览器访问 http://localhost:5173                          ║
echo ║  3. 注册账户并开始使用                                          ║
echo ║                                                              ║
echo ║  如需重新配置，可再次运行此脚本                                  ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

pause
