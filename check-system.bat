@echo off
chcp 65001 >nul
title FamilyOAPlatform 系统检查

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                🔍 FamilyOAPlatform 系统检查                    ║
echo ║                                                              ║
echo ║  正在检查系统环境和依赖项...                                     ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:: 创建检查报告文件
set REPORT_FILE=logs\system-check-%date:~0,4%%date:~5,2%%date:~8,2%-%time:~0,2%%time:~3,2%%time:~6,2%.txt
if not exist "logs" mkdir logs

echo FamilyOAPlatform 系统检查报告 > "%REPORT_FILE%"
echo 检查时间: %date% %time% >> "%REPORT_FILE%"
echo ═══════════════════════════════════════════════════════════════ >> "%REPORT_FILE%"
echo. >> "%REPORT_FILE%"

set PASS_COUNT=0
set FAIL_COUNT=0
set WARN_COUNT=0

echo 🖥️  系统信息检查
echo 系统信息检查 >> "%REPORT_FILE%"
echo ─────────────────────────────────────────────────────────────── >> "%REPORT_FILE%"

:: 检查Windows版本
echo [检查] Windows版本...
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo   Windows版本: %VERSION%
echo Windows版本: %VERSION% >> "%REPORT_FILE%"

:: 判断版本是否符合要求
if "%VERSION%" geq "10.0" (
    echo   ✅ Windows版本符合要求 ^(需要10.0+^)
    echo ✅ Windows版本符合要求 >> "%REPORT_FILE%"
    set /a PASS_COUNT+=1
) else (
    echo   ❌ Windows版本过低 ^(当前: %VERSION%, 需要: 10.0+^)
    echo ❌ Windows版本过低 >> "%REPORT_FILE%"
    set /a FAIL_COUNT+=1
)

:: 检查系统架构
echo [检查] 系统架构...
if "%PROCESSOR_ARCHITECTURE%"=="AMD64" (
    echo   ✅ 64位系统
    echo ✅ 64位系统 >> "%REPORT_FILE%"
    set /a PASS_COUNT+=1
) else (
    echo   ⚠️  32位系统 ^(推荐64位^)
    echo ⚠️ 32位系统 >> "%REPORT_FILE%"
    set /a WARN_COUNT+=1
)

:: 检查内存
echo [检查] 系统内存...
for /f "skip=1" %%p in ('wmic computersystem get TotalPhysicalMemory') do (
    set /a MEMORY_GB=%%p/1024/1024/1024
    goto :memory_done
)
:memory_done
echo   系统内存: %MEMORY_GB%GB
echo 系统内存: %MEMORY_GB%GB >> "%REPORT_FILE%"

if %MEMORY_GB% geq 8 (
    echo   ✅ 内存充足 ^(推荐8GB+^)
    echo ✅ 内存充足 >> "%REPORT_FILE%"
    set /a PASS_COUNT+=1
) else if %MEMORY_GB% geq 4 (
    echo   ⚠️  内存满足最低要求 ^(4GB+^)
    echo ⚠️ 内存满足最低要求 >> "%REPORT_FILE%"
    set /a WARN_COUNT+=1
) else (
    echo   ❌ 内存不足 ^(当前: %MEMORY_GB%GB, 最低需要: 4GB^)
    echo ❌ 内存不足 >> "%REPORT_FILE%"
    set /a FAIL_COUNT+=1
)

:: 检查磁盘空间
echo [检查] 磁盘空间...
for /f "tokens=3" %%a in ('dir /-c ^| find "bytes free"') do set DISK_FREE=%%a
set /a DISK_FREE_GB=%DISK_FREE:~0,-9%/1024/1024/1024
echo   可用空间: %DISK_FREE_GB%GB
echo 可用磁盘空间: %DISK_FREE_GB%GB >> "%REPORT_FILE%"

if %DISK_FREE_GB% geq 5 (
    echo   ✅ 磁盘空间充足 ^(推荐5GB+^)
    echo ✅ 磁盘空间充足 >> "%REPORT_FILE%"
    set /a PASS_COUNT+=1
) else if %DISK_FREE_GB% geq 2 (
    echo   ⚠️  磁盘空间满足最低要求 ^(2GB+^)
    echo ⚠️ 磁盘空间满足最低要求 >> "%REPORT_FILE%"
    set /a WARN_COUNT+=1
) else (
    echo   ❌ 磁盘空间不足 ^(当前: %DISK_FREE_GB%GB, 最低需要: 2GB^)
    echo ❌ 磁盘空间不足 >> "%REPORT_FILE%"
    set /a FAIL_COUNT+=1
)

echo.
echo 🔧 软件依赖检查
echo. >> "%REPORT_FILE%"
echo 软件依赖检查 >> "%REPORT_FILE%"
echo ─────────────────────────────────────────────────────────────── >> "%REPORT_FILE%"

:: 检查Node.js
echo [检查] Node.js...
node --version >nul 2>&1
if %errorLevel% equ 0 (
    for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
    echo   已安装: %NODE_VERSION%
    echo Node.js版本: %NODE_VERSION% >> "%REPORT_FILE%"
    
    :: 检查版本
    set NODE_MAJOR=%NODE_VERSION:~1,2%
    if %NODE_MAJOR% geq 18 (
        echo   ✅ Node.js版本符合要求 ^(需要v18+^)
        echo ✅ Node.js版本符合要求 >> "%REPORT_FILE%"
        set /a PASS_COUNT+=1
    ) else (
        echo   ⚠️  Node.js版本过低 ^(当前: %NODE_VERSION%, 需要: v18+^)
        echo ⚠️ Node.js版本过低 >> "%REPORT_FILE%"
        set /a WARN_COUNT+=1
    )
) else (
    echo   ❌ 未安装Node.js
    echo ❌ 未安装Node.js >> "%REPORT_FILE%"
    set /a FAIL_COUNT+=1
)

:: 检查npm
echo [检查] npm...
npm --version >nul 2>&1
if %errorLevel% equ 0 (
    for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
    echo   已安装: v%NPM_VERSION%
    echo   ✅ npm可用
    echo npm版本: v%NPM_VERSION% >> "%REPORT_FILE%"
    echo ✅ npm可用 >> "%REPORT_FILE%"
    set /a PASS_COUNT+=1
) else (
    echo   ❌ npm不可用
    echo ❌ npm不可用 >> "%REPORT_FILE%"
    set /a FAIL_COUNT+=1
)

:: 检查MySQL
echo [检查] MySQL...
if exist "mysql\bin\mysql.exe" (
    echo   ✅ 检测到本地MySQL
    echo ✅ 检测到本地MySQL >> "%REPORT_FILE%"
    set /a PASS_COUNT+=1
) else (
    mysql --version >nul 2>&1
    if %errorLevel% equ 0 (
        for /f "tokens=*" %%i in ('mysql --version') do set MYSQL_VERSION=%%i
        echo   已安装: %MYSQL_VERSION%
        echo   ✅ 系统MySQL可用
        echo MySQL版本: %MYSQL_VERSION% >> "%REPORT_FILE%"
        echo ✅ 系统MySQL可用 >> "%REPORT_FILE%"
        set /a PASS_COUNT+=1
    ) else (
        echo   ⚠️  未检测到MySQL ^(将自动安装便携版^)
        echo ⚠️ 未检测到MySQL >> "%REPORT_FILE%"
        set /a WARN_COUNT+=1
    )
)

echo.
echo 🌐 网络连接检查
echo. >> "%REPORT_FILE%"
echo 网络连接检查 >> "%REPORT_FILE%"
echo ─────────────────────────────────────────────────────────────── >> "%REPORT_FILE%"

:: 检查基本网络连接
echo [检查] 基本网络连接...
ping -n 1 8.8.8.8 >nul 2>&1
if %errorLevel% equ 0 (
    echo   ✅ 网络连接正常
    echo ✅ 网络连接正常 >> "%REPORT_FILE%"
    set /a PASS_COUNT+=1
) else (
    echo   ❌ 网络连接失败
    echo ❌ 网络连接失败 >> "%REPORT_FILE%"
    set /a FAIL_COUNT+=1
)

:: 检查HTTPS连接
echo [检查] HTTPS连接...
powershell -Command "try { Invoke-WebRequest -Uri 'https://www.google.com' -UseBasicParsing -TimeoutSec 5 | Out-Null; exit 0 } catch { exit 1 }" >nul 2>&1
if %errorLevel% equ 0 (
    echo   ✅ HTTPS连接正常
    echo ✅ HTTPS连接正常 >> "%REPORT_FILE%"
    set /a PASS_COUNT+=1
) else (
    echo   ⚠️  HTTPS连接可能受限 ^(可能影响依赖下载^)
    echo ⚠️ HTTPS连接可能受限 >> "%REPORT_FILE%"
    set /a WARN_COUNT+=1
)

:: 检查Google AI API连接
echo [检查] Google AI API连接...
powershell -Command "try { Invoke-WebRequest -Uri 'https://generativelanguage.googleapis.com' -UseBasicParsing -TimeoutSec 5 | Out-Null; exit 0 } catch { exit 1 }" >nul 2>&1
if %errorLevel% equ 0 (
    echo   ✅ Google AI API可访问
    echo ✅ Google AI API可访问 >> "%REPORT_FILE%"
    set /a PASS_COUNT+=1
) else (
    echo   ⚠️  Google AI API连接失败 ^(可能影响AI功能^)
    echo ⚠️ Google AI API连接失败 >> "%REPORT_FILE%"
    set /a WARN_COUNT+=1
)

echo.
echo 🔒 端口检查
echo. >> "%REPORT_FILE%"
echo 端口检查 >> "%REPORT_FILE%"
echo ─────────────────────────────────────────────────────────────── >> "%REPORT_FILE%"

:: 检查端口3000
echo [检查] 端口3000 ^(后端API^)...
netstat -an | find ":3000" >nul
if %errorLevel% equ 0 (
    echo   ⚠️  端口3000已被占用
    echo ⚠️ 端口3000已被占用 >> "%REPORT_FILE%"
    set /a WARN_COUNT+=1
) else (
    echo   ✅ 端口3000可用
    echo ✅ 端口3000可用 >> "%REPORT_FILE%"
    set /a PASS_COUNT+=1
)

:: 检查端口5173
echo [检查] 端口5173 ^(前端Web^)...
netstat -an | find ":5173" >nul
if %errorLevel% equ 0 (
    echo   ⚠️  端口5173已被占用
    echo ⚠️ 端口5173已被占用 >> "%REPORT_FILE%"
    set /a WARN_COUNT+=1
) else (
    echo   ✅ 端口5173可用
    echo ✅ 端口5173可用 >> "%REPORT_FILE%"
    set /a PASS_COUNT+=1
)

:: 检查端口3306
echo [检查] 端口3306 ^(MySQL^)...
netstat -an | find ":3306" >nul
if %errorLevel% equ 0 (
    echo   ⚠️  端口3306已被占用
    echo ⚠️ 端口3306已被占用 >> "%REPORT_FILE%"
    set /a WARN_COUNT+=1
) else (
    echo   ✅ 端口3306可用
    echo ✅ 端口3306可用 >> "%REPORT_FILE%"
    set /a PASS_COUNT+=1
)

echo.
echo 📊 检查结果汇总
echo. >> "%REPORT_FILE%"
echo 检查结果汇总 >> "%REPORT_FILE%"
echo ═══════════════════════════════════════════════════════════════ >> "%REPORT_FILE%"

set /a TOTAL_CHECKS=%PASS_COUNT%+%WARN_COUNT%+%FAIL_COUNT%

echo   ✅ 通过: %PASS_COUNT%
echo   ⚠️  警告: %WARN_COUNT%
echo   ❌ 失败: %FAIL_COUNT%
echo   📊 总计: %TOTAL_CHECKS%

echo ✅ 通过: %PASS_COUNT% >> "%REPORT_FILE%"
echo ⚠️ 警告: %WARN_COUNT% >> "%REPORT_FILE%"
echo ❌ 失败: %FAIL_COUNT% >> "%REPORT_FILE%"
echo 📊 总计: %TOTAL_CHECKS% >> "%REPORT_FILE%"

echo.
if %FAIL_COUNT% equ 0 (
    if %WARN_COUNT% equ 0 (
        echo ╔══════════════════════════════════════════════════════════════╗
        echo ║                    🎉 系统检查完美通过！                       ║
        echo ║                                                              ║
        echo ║  您的系统完全满足FamilyOAPlatform的运行要求                     ║
        echo ║  可以直接运行 install.bat 开始安装                             ║
        echo ╚══════════════════════════════════════════════════════════════╝
        echo 🎉 系统检查完美通过 >> "%REPORT_FILE%"
    ) else (
        echo ╔══════════════════════════════════════════════════════════════╗
        echo ║                    ✅ 系统检查基本通过                         ║
        echo ║                                                              ║
        echo ║  存在一些警告项，但不影响正常安装和使用                          ║
        echo ║  可以运行 install.bat 开始安装                                 ║
        echo ╚══════════════════════════════════════════════════════════════╝
        echo ✅ 系统检查基本通过 >> "%REPORT_FILE%"
    )
) else (
    echo ╔══════════════════════════════════════════════════════════════╗
    echo ║                    ❌ 系统检查发现问题                         ║
    echo ║                                                              ║
    echo ║  请解决上述失败项后再进行安装                                    ║
    echo ║  或查看详细报告: %REPORT_FILE%
    echo ╚══════════════════════════════════════════════════════════════╝
    echo ❌ 系统检查发现问题 >> "%REPORT_FILE%"
)

echo.
echo 📄 详细报告已保存到: %REPORT_FILE%
echo.

pause
