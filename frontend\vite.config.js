import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  server: {
    port: 5173,
    host: '0.0.0.0', // 明确设置为0.0.0.0以允许外部访问
    proxy: {
      '/api': {
        target: 'http://localhost:3002',
        changeOrigin: true,
        secure: false,
        configure: (proxy) => {
          // 动态设置代理目标，根据请求的host来确定后端地址
          proxy.on('proxyReq', (proxyReq, req) => {
            const host = req.headers.host;
            if (host && !host.startsWith('localhost') && !host.startsWith('127.0.0.1')) {
              // 如果访问的不是localhost，则将后端地址也改为相同的IP
              const ip = host.split(':')[0];
              proxyReq.path = proxyReq.path.replace(/^\/api/, '');
              proxyReq.host = `${ip}:3002`;
              proxyReq.headers.host = `${ip}:3002`;
            }
          });
        }
      }
    }
  },
  build: {
    outDir: 'dist',
    sourcemap: false,
    chunkSizeWarningLimit: 1000,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          elementPlus: ['element-plus', '@element-plus/icons-vue'],
          utils: ['axios']
        }
      }
    }
  },
  define: {
    __VUE_OPTIONS_API__: false,
    __VUE_PROD_DEVTOOLS__: false
  }
})
