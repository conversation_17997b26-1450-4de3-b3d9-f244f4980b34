<template>
  <div v-if="hasError" class="error-boundary">
    <el-result
      icon="error"
      title="页面出现错误"
      :sub-title="errorMessage"
    >
      <template #extra>
        <el-space>
          <el-button type="primary" @click="handleReload">
            刷新页面
          </el-button>
          <el-button @click="handleGoHome">
            返回首页
          </el-button>
          <el-button 
            v-if="showDetails" 
            type="info" 
            @click="toggleDetails"
          >
            {{ showErrorDetails ? '隐藏详情' : '显示详情' }}
          </el-button>
        </el-space>
      </template>
    </el-result>
    
    <el-collapse v-if="showDetails && showErrorDetails" class="error-details">
      <el-collapse-item title="错误详情" name="details">
        <div class="error-info">
          <p><strong>错误信息:</strong> {{ error?.message || '未知错误' }}</p>
          <p><strong>发生时间:</strong> {{ errorTime }}</p>
          <p><strong>页面路径:</strong> {{ currentPath }}</p>
          <p><strong>用户代理:</strong> {{ userAgent }}</p>
        </div>
        
        <div v-if="error?.stack" class="error-stack">
          <p><strong>错误堆栈:</strong></p>
          <pre>{{ error.stack }}</pre>
        </div>
        
        <div class="error-actions">
          <el-button 
            size="small" 
            type="warning" 
            @click="reportError"
          >
            报告错误
          </el-button>
          <el-button 
            size="small" 
            @click="copyErrorInfo"
          >
            复制错误信息
          </el-button>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
  
  <slot v-else />
</template>

<script setup>
import { ref, onMounted, onErrorCaptured, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'

const router = useRouter()

// 错误状态
const hasError = ref(false)
const error = ref(null)
const errorTime = ref('')
const currentPath = ref('')
const showErrorDetails = ref(false)

// 计算属性
const errorMessage = computed(() => {
  if (!error.value) return '页面加载失败'
  
  // 根据错误类型返回用户友好的消息
  if (error.value.message?.includes('ChunkLoadError')) {
    return '页面资源加载失败，可能是网络问题或版本更新'
  }
  
  if (error.value.message?.includes('Network Error')) {
    return '网络连接失败，请检查网络设置'
  }
  
  if (error.value.message?.includes('timeout')) {
    return '请求超时，请稍后重试'
  }
  
  return '页面运行时出现错误'
})

const showDetails = computed(() => {
  return import.meta.env.DEV || error.value?.showDetails
})

const userAgent = computed(() => {
  return navigator.userAgent
})

// 错误捕获
onErrorCaptured((err, instance, info) => {
  console.error('Vue Error Captured:', err, info)
  handleError(err, info)
  return false // 阻止错误继续传播
})

// 全局错误处理
const handleError = (err, info = '') => {
  hasError.value = true
  error.value = err
  errorTime.value = new Date().toLocaleString()
  currentPath.value = router.currentRoute.value.fullPath
  
  // 记录错误到控制台
  console.error('Error Boundary Caught:', {
    error: err,
    info,
    time: errorTime.value,
    path: currentPath.value,
    userAgent: userAgent.value
  })
  
  // 发送错误报告到服务器（可选）
  if (import.meta.env.PROD) {
    sendErrorReport(err, info)
  }
}

// 发送错误报告
const sendErrorReport = async (err, info) => {
  try {
    // 这里可以调用错误报告API
    const errorReport = {
      message: err.message,
      stack: err.stack,
      info,
      url: window.location.href,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString()
    }
    
    // 示例：发送到错误监控服务
    // await api.post('/api/errors/report', errorReport)
    console.log('Error report prepared:', errorReport)
  } catch (reportError) {
    console.error('Failed to send error report:', reportError)
  }
}

// 事件处理
const handleReload = () => {
  window.location.reload()
}

const handleGoHome = () => {
  router.push('/dashboard')
}

const toggleDetails = () => {
  showErrorDetails.value = !showErrorDetails.value
}

const reportError = async () => {
  try {
    await ElMessageBox.confirm(
      '是否要将错误信息发送给开发团队？这将有助于我们改进产品。',
      '报告错误',
      {
        confirmButtonText: '发送',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await sendErrorReport(error.value, '用户主动报告')
    ElMessage.success('错误报告已发送，感谢您的反馈！')
  } catch (err) {
    if (err !== 'cancel') {
      ElMessage.error('发送错误报告失败')
    }
  }
}

const copyErrorInfo = async () => {
  const errorInfo = `
错误信息: ${error.value?.message || '未知错误'}
发生时间: ${errorTime.value}
页面路径: ${currentPath.value}
用户代理: ${userAgent.value}
错误堆栈: ${error.value?.stack || '无'}
  `.trim()
  
  try {
    await navigator.clipboard.writeText(errorInfo)
    ElMessage.success('错误信息已复制到剪贴板')
  } catch (err) {
    console.error('Failed to copy error info:', err)
    ElMessage.error('复制失败，请手动选择文本复制')
  }
}

// 监听全局未捕获的错误
onMounted(() => {
  // JavaScript错误
  window.addEventListener('error', (event) => {
    handleError(event.error || new Error(event.message), 'Global Error')
  })
  
  // Promise拒绝错误
  window.addEventListener('unhandledrejection', (event) => {
    handleError(event.reason || new Error('Unhandled Promise Rejection'), 'Promise Rejection')
  })
  
  // 资源加载错误
  window.addEventListener('error', (event) => {
    if (event.target !== window) {
      handleError(new Error(`Resource load error: ${event.target.src || event.target.href}`), 'Resource Error')
    }
  }, true)
})
</script>

<style scoped>
.error-boundary {
  padding: 20px;
  min-height: 400px;
}

.error-details {
  margin-top: 20px;
  max-width: 800px;
}

.error-info {
  margin-bottom: 20px;
}

.error-info p {
  margin: 8px 0;
  word-break: break-all;
}

.error-stack {
  margin: 20px 0;
}

.error-stack pre {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
  line-height: 1.4;
  max-height: 300px;
}

.error-actions {
  margin-top: 15px;
}
</style>
