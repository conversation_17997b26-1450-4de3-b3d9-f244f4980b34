const axios = require('axios');

async function testLogin() {
  try {
    console.log('测试登录API...');
    
    const response = await axios.post('http://localhost:3002/api/auth/login', {
      username: '<PERSON><PERSON><PERSON><PERSON>',
      password: '<PERSON><PERSON><PERSON><PERSON>'
    });
    
    console.log('✅ 登录成功!');
    console.log('响应数据:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.log('❌ 无法连接到服务器 - 后端服务器可能没有运行');
      console.log('请确保后端服务器在端口3002上运行');
    } else {
      console.log('❌ 登录失败:', error.response?.data || error.message);
    }
  }
}

testLogin();
