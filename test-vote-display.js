const axios = require('axios');

async function testVoteDisplay() {
  try {
    console.log('测试投票显示优化...');
    
    // 1. 登录获取token
    const loginResponse = await axios.post('http://localhost:3002/api/auth/login', {
      username: 'l<PERSON><PERSON><PERSON>',
      password: 'lin<PERSON><PERSON>'
    });
    
    const token = loginResponse.data.token;
    console.log('✅ 登录成功');
    
    // 2. 创建测试提案
    const proposalResponse = await axios.post('http://localhost:3002/api/proposals', {
      title: '测试投票显示优化',
      description: '这是一个用于测试投票结果显示优化的提案',
      solution: '优化投票结果的视觉呈现',
      priority: 'medium'
    }, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    const proposalId = proposalResponse.data.proposalId;
    console.log('✅ 创建测试提案成功，ID:', proposalId);
    
    // 3. 添加一些测试投票
    console.log('📊 添加测试投票...');
    
    // 赞成票
    await axios.post(`http://localhost:3002/api/proposals/${proposalId}/votes`, {
      voteType: 'approve'
    }, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    console.log('✅ 添加赞成票');
    
    // 4. 获取提案详情查看投票结果
    const detailResponse = await axios.get(`http://localhost:3002/api/proposals/${proposalId}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    const votes = detailResponse.data.votes || [];
    const approveVotes = votes.filter(v => v.vote_type === 'approve').length;
    const rejectVotes = votes.filter(v => v.vote_type === 'reject').length;
    const abstainVotes = votes.filter(v => v.vote_type === 'abstain').length;
    
    console.log('\n📊 投票结果统计:');
    console.log(`赞成票: ${approveVotes} (绿色背景 #10b981)`);
    console.log(`反对票: ${rejectVotes} (红色背景 #ef4444)`);
    console.log(`弃权票: ${abstainVotes} (深灰色背景 #6b7280)`);
    
    console.log('\n🎨 样式优化完成:');
    console.log('- 投票数字现在显示在彩色背景上');
    console.log('- 赞成票：绿色背景 + 白色数字');
    console.log('- 反对票：红色背景 + 白色数字');
    console.log('- 弃权票：深灰色背景 + 白色数字');
    console.log(`\n🌐 请访问 http://localhost:5173/proposals/${proposalId} 查看效果`);
    
  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
  }
}

testVoteDisplay();
