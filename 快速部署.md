# 🚀 FamilyOAPlatform Windows 快速部署指南

## 📋 概述

本指南帮助您在 **5-10分钟内** 在Windows系统上快速部署FamilyOAPlatform，适用于本地开发和测试环境。

### 🎯 部署目标
- **系统**: Windows 10/11
- **环境**: 本地开发/测试
- **时间**: 5-10分钟
- **技能**: 无需专业技术背景

## ✅ 前置条件检查

### 系统要求
- [ ] Windows 10 版本1903或更高 / Windows 11
- [ ] 至少4GB可用内存
- [ ] 至少2GB可用磁盘空间
- [ ] 稳定的网络连接（用于下载依赖和AI功能）

### 权限要求
- [ ] 管理员权限（用于安装软件）
- [ ] 防火墙允许Node.js网络访问

### 预检查脚本
```batch
# 下载项目后，双击运行 check-system.bat 进行系统检查
```

## 📦 快速部署步骤

### 步骤1: 下载项目 (1分钟)

1. **下载项目ZIP包**
   - 访问GitHub项目页面
   - 点击绿色的 "Code" 按钮
   - 选择 "Download ZIP"
   - 下载完成后解压到 `C:\FamilyOAPlatform`

2. **验证文件结构**
   ```
   C:\FamilyOAPlatform\
   ├── backend\
   ├── frontend\
   ├── database\
   ├── scripts\          # 新增：自动化脚本目录
   ├── install.bat       # 新增：一键安装脚本
   ├── start.bat         # 新增：启动脚本
   └── 快速部署.md
   ```

### 步骤2: 一键安装 (3-5分钟)

1. **运行安装脚本**
   - 右键点击 `install.bat`
   - 选择 "以管理员身份运行"
   - 等待安装完成（会自动完成以下操作）：
     - 检测并安装Node.js 18.x LTS
     - 安装MySQL 8.0便携版
     - 安装项目依赖
     - 初始化数据库
     - 生成配置文件

2. **安装过程说明**
   ```
   [1/6] 检查系统环境...
   [2/6] 下载并安装Node.js...
   [3/6] 配置MySQL数据库...
   [4/6] 安装项目依赖...
   [5/6] 初始化数据库...
   [6/6] 生成配置文件...
   
   ✅ 安装完成！
   ```

### 步骤3: 配置Google AI API (2分钟)

1. **获取API密钥**
   - 访问 [Google AI Studio](https://makersuite.google.com/app/apikey)
   - 登录Google账户
   - 点击 "Create API Key"
   - 复制生成的API密钥

2. **配置API密钥**
   - 双击运行 `config-wizard.bat`
   - 按提示粘贴API密钥
   - 脚本会自动更新配置文件

   ```batch
   请输入您的Google AI API密钥:
   > AIzaSyC-your-api-key-here
   
   ✅ API密钥配置成功！
   ```

### 步骤4: 启动服务 (1分钟)

1. **启动应用**
   - 双击运行 `start.bat`
   - 等待服务启动完成

   ```batch
   [启动] 后端服务 (端口: 3000)...
   [启动] 前端服务 (端口: 5173)...
   [启动] 数据库服务...
   
   🎉 所有服务启动成功！
   
   访问地址: http://localhost:5173
   API地址: http://localhost:3000
   
   按任意键打开浏览器...
   ```

2. **验证部署**
   - 浏览器自动打开 `http://localhost:5173`
   - 看到登录页面表示部署成功

## 🧪 快速功能测试

### 基础功能测试 (2分钟)

1. **用户注册测试**
   - 点击 "注册" 按钮
   - 填写测试信息：
     - 用户名: `test`
     - 密码: `test123`
     - 真实姓名: `测试用户`
   - 点击注册

2. **登录测试**
   - 使用刚注册的账户登录
   - 成功进入仪表板页面

3. **提案功能测试**
   - 点击 "创建新提案"
   - 填写提案信息：
     - 标题: `测试提案`
     - 描述: `这是一个测试提案`
   - 点击保存

4. **AI功能测试**
   - 在提案详情页点击 "AI扩展"
   - 等待AI处理完成
   - 查看扩展后的内容

### 测试检查清单
- [ ] 用户注册和登录正常
- [ ] 提案创建和查看正常
- [ ] AI内容扩展功能正常
- [ ] 投票和评论功能正常
- [ ] 页面响应速度正常

## 🔧 自动化脚本说明

### install.bat - 一键安装脚本
```batch
@echo off
echo 🚀 FamilyOAPlatform 快速安装向导
echo.

# 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ 请以管理员身份运行此脚本
    pause
    exit /b 1
)

# 检查系统环境
call scripts\check-system.bat

# 安装Node.js
call scripts\install-nodejs.bat

# 配置数据库
call scripts\setup-database.bat

# 安装依赖
call scripts\install-dependencies.bat

# 初始化数据库
call scripts\init-database.bat

# 生成配置文件
call scripts\generate-config.bat

echo.
echo ✅ 安装完成！
echo 请运行 config-wizard.bat 配置Google AI API密钥
echo 然后运行 start.bat 启动服务
pause
```

### start.bat - 启动脚本
```batch
@echo off
title FamilyOAPlatform 服务管理器

echo 🚀 启动 FamilyOAPlatform 服务...
echo.

# 检查配置文件
if not exist "backend\.env" (
    echo ❌ 配置文件不存在，请先运行 install.bat
    pause
    exit /b 1
)

# 启动数据库
echo [1/3] 启动数据库服务...
call scripts\start-database.bat

# 启动后端
echo [2/3] 启动后端服务...
start "后端服务" cmd /k "cd backend && npm start"

# 等待后端启动
timeout /t 5 /nobreak >nul

# 启动前端
echo [3/3] 启动前端服务...
start "前端服务" cmd /k "cd frontend && npm run dev"

# 等待前端启动
timeout /t 10 /nobreak >nul

echo.
echo ✅ 所有服务启动成功！
echo.
echo 📱 访问地址: http://localhost:5173
echo 🔧 API地址: http://localhost:3000
echo.
echo 按任意键打开浏览器...
pause >nul

start http://localhost:5173
```

### config-wizard.bat - 配置向导
```batch
@echo off
title FamilyOAPlatform 配置向导

echo 🔧 FamilyOAPlatform 配置向导
echo.

# 检查配置文件
if not exist "backend\.env" (
    echo ❌ 请先运行 install.bat 完成安装
    pause
    exit /b 1
)

echo 请配置Google AI API密钥以启用AI功能：
echo.
echo 1. 访问 https://makersuite.google.com/app/apikey
echo 2. 登录Google账户并创建API密钥
echo 3. 复制API密钥并粘贴到下方
echo.

set /p api_key="请输入您的Google AI API密钥: "

if "%api_key%"=="" (
    echo ❌ API密钥不能为空
    pause
    exit /b 1
)

# 更新配置文件
powershell -Command "(Get-Content backend\.env) -replace 'your_google_ai_api_key_here', '%api_key%' | Set-Content backend\.env"

echo.
echo ✅ API密钥配置成功！
echo 现在可以运行 start.bat 启动服务
echo.
pause
```

## 🛠️ 故障排查

### 常见问题及解决方案

#### 1. 安装失败
**问题**: install.bat 运行失败
**解决方案**:
- 确保以管理员身份运行
- 检查网络连接
- 临时关闭杀毒软件
- 查看错误日志: `logs\install-error.log`

#### 2. 端口被占用
**问题**: 启动时提示端口3000或5173被占用
**解决方案**:
```batch
# 查看端口占用
netstat -ano | findstr :3000
netstat -ano | findstr :5173

# 结束占用进程
taskkill /PID <进程ID> /F
```

#### 3. 数据库连接失败
**问题**: 后端无法连接数据库
**解决方案**:
- 检查MySQL服务是否启动
- 运行 `scripts\restart-database.bat`
- 查看数据库日志: `mysql\data\error.log`

#### 4. AI功能不工作
**问题**: AI扩展或仲裁功能失败
**解决方案**:
- 检查API密钥是否正确配置
- 确保网络可以访问Google服务
- 重新运行 `config-wizard.bat`

#### 5. 前端页面无法访问
**问题**: 浏览器无法打开应用页面
**解决方案**:
- 检查防火墙设置
- 确保Node.js进程正在运行
- 尝试访问 `http://127.0.0.1:5173`

### 日志文件位置
- 安装日志: `logs\install.log`
- 后端日志: `backend\logs\`
- 前端日志: `frontend\logs\`
- 数据库日志: `mysql\data\error.log`

### 重置和清理
```batch
# 完全重置（删除所有数据）
reset-all.bat

# 仅重置配置
reset-config.bat

# 清理日志文件
clean-logs.bat
```

## 📞 获取帮助

如果遇到问题：

1. **查看日志文件** - 检查相应的日志文件获取详细错误信息
2. **运行诊断脚本** - 执行 `diagnose.bat` 获取系统诊断报告
3. **重新安装** - 运行 `reset-all.bat` 后重新执行安装流程

---

## 📁 自动化脚本文件说明

项目包含以下自动化脚本，简化部署和管理：

### 核心脚本
- **`install.bat`** - 一键安装脚本，自动完成环境配置
- **`start.bat`** - 启动所有服务（数据库、后端、前端）
- **`stop.bat`** - 停止所有服务
- **`config-wizard.bat`** - 配置向导，设置API密钥和参数

### 辅助脚本
- **`check-system.bat`** - 系统环境检查和诊断
- **`reset-all.bat`** - 完全重置，删除所有数据和配置
- **`reset-config.bat`** - 仅重置配置文件
- **`clean-logs.bat`** - 清理日志文件

### 使用建议
1. **首次部署**：`check-system.bat` → `install.bat` → `config-wizard.bat` → `start.bat`
2. **日常使用**：`start.bat` 启动，`stop.bat` 停止
3. **遇到问题**：查看 `logs\` 目录中的日志文件
4. **完全重装**：`reset-all.bat` → 重新执行首次部署流程

## 🔧 高级配置选项

### 自定义端口配置
如果默认端口被占用，可以修改：

1. **后端端口** (默认3000)：
   - 编辑 `backend\.env` 文件中的 `PORT=3000`
   - 同时更新 `frontend\.env.development` 中的API地址

2. **前端端口** (默认5173)：
   - 编辑 `frontend\vite.config.js` 中的 `port: 5173`

### 数据库配置选项
支持以下数据库配置：

1. **本地MySQL便携版** (默认，推荐)
2. **系统已安装的MySQL**
3. **远程MySQL服务器**

配置方法：运行 `config-wizard.bat` 选择数据库配置选项

### 性能优化建议
- **内存**: 推荐8GB以上，最低4GB
- **存储**: SSD硬盘可显著提升性能
- **网络**: 稳定的网络连接确保AI功能正常

## 📱 移动设备访问

部署完成后，同一局域网内的移动设备也可以访问：

1. **获取电脑IP地址**：
   ```cmd
   ipconfig | findstr "IPv4"
   ```

2. **移动设备访问**：
   - 在手机/平板浏览器中输入：`http://电脑IP:5173`
   - 例如：`http://*************:5173`

3. **防火墙设置**：
   - Windows可能需要允许Node.js通过防火墙
   - 首次启动时会弹出防火墙提示，选择"允许访问"

## 🛡️ 安全注意事项

### 本地开发环境
- 默认配置适用于本地开发和测试
- 数据库使用简单密码，仅限本地访问
- 不建议在生产环境直接使用

### API密钥安全
- Google AI API密钥请妥善保管
- 不要将包含API密钥的配置文件分享给他人
- 定期检查API密钥使用情况

### 数据备份
- 重要数据请定期备份
- 数据库文件位置：`mysql\data\`
- 配置文件位置：`backend\.env`

## 🆘 常见问题快速解决

### Q: 安装过程中下载失败
**A**: 检查网络连接，或使用VPN。也可以手动下载Node.js和MySQL安装包。

### Q: 端口被占用怎么办？
**A**: 运行 `netstat -ano | findstr :3000` 查看占用进程，使用 `taskkill /PID <进程ID> /F` 结束进程。

### Q: AI功能不工作
**A**: 检查Google AI API密钥是否正确配置，确保网络可以访问Google服务。

### Q: 数据库连接失败
**A**: 确保MySQL服务正在运行，检查 `backend\.env` 中的数据库配置。

### Q: 前端页面打不开
**A**: 检查防火墙设置，确保允许Node.js网络访问。

### Q: 如何完全卸载？
**A**: 运行 `reset-all.bat` 删除所有数据，然后删除项目文件夹。

## 📞 技术支持

### 日志文件位置
- 安装日志：`logs\install-*.log`
- 启动日志：`logs\start-*.log`
- 系统检查：`logs\system-check-*.txt`
- 应用日志：`backend\logs\` 和 `frontend\logs\`

### 诊断工具
- 运行 `check-system.bat` 获取完整的系统诊断报告
- 查看最新的日志文件了解详细错误信息

### 重置选项
- **完全重置**：`reset-all.bat` (删除所有数据)
- **配置重置**：`reset-config.bat` (仅重置配置)
- **日志清理**：`clean-logs.bat` (清理日志文件)

---

**🎉 恭喜！您已成功部署FamilyOAPlatform！现在可以开始使用这个强大的家庭沟通平台了。**

**💡 提示**：建议将项目文件夹添加到Windows Defender排除列表，以提高性能并避免误报。
