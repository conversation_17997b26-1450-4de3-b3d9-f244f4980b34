const axios = require('axios');

async function testLLMAPI() {
  try {
    console.log('测试LLM API...');
    
    // 首先获取一个有效的token
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      username: 'admin',
      password: 'admin123'
    });

    const token = loginResponse.data.token;
    console.log('登录成功，获取token:', token.substring(0, 20) + '...');

    // 测试LLM扩展API
    const response = await axios.post('http://localhost:3001/api/llm/expand', {
      text: '我想在家里安装一个新的空调系统',
      type: 'description'
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('LLM API测试成功:');
    console.log('原文:', response.data.originalText);
    console.log('扩展结果:', response.data.expandedText.substring(0, 200) + '...');
    
  } catch (error) {
    console.error('LLM API测试失败:');
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('错误信息:', error.response.data);
    } else if (error.request) {
      console.error('网络错误:', error.message);
    } else {
      console.error('请求配置错误:', error.message);
    }
  }
}

testLLMAPI();
