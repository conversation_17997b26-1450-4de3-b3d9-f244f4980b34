-- Family OA Platform Database Schema
-- 家庭办公平台数据库架构

-- Create database
CREATE DATABASE IF NOT EXISTS family_oa_platform 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE family_oa_platform;

-- Users table - 用户表
CREATE TABLE IF NOT EXISTS users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    email VARCHAR(100) UNIQUE NOT NULL COMMENT '邮箱',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    real_name VARCHAR(50) NOT NULL COMMENT '真实姓名',
    avatar_url VARCHAR(255) COMMENT '头像链接',
    role ENUM('admin', 'member') DEFAULT 'member' COMMENT '角色',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_username (username),
    INDEX idx_email (email)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- Proposals table - 提案表
CREATE TABLE IF NOT EXISTS proposals (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL COMMENT '提案标题',
    description TEXT NOT NULL COMMENT '提案描述',
    expanded_description TEXT COMMENT 'AI扩展描述',
    solution TEXT COMMENT '解决方案',
    expanded_solution TEXT COMMENT 'AI扩展解决方案',
    status ENUM('draft', 'pending', 'approved', 'rejected', 'completed') DEFAULT 'draft' COMMENT '状态',
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium' COMMENT '优先级',
    creator_id INT NOT NULL COMMENT '创建者ID',
    assigned_to INT COMMENT '分配给',
    ai_approval_score DECIMAL(3,2) COMMENT 'AI评分',
    ai_opinion TEXT COMMENT 'AI意见',
    due_date DATE COMMENT '截止日期',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_creator (creator_id),
    INDEX idx_status (status),
    INDEX idx_priority (priority),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='提案表';

-- Comments table - 评论表
CREATE TABLE IF NOT EXISTS comments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    proposal_id INT NOT NULL COMMENT '提案ID',
    user_id INT NOT NULL COMMENT '用户ID',
    content TEXT NOT NULL COMMENT '评论内容',
    expanded_content TEXT COMMENT 'AI扩展内容',
    comment_type ENUM('support', 'oppose', 'neutral', 'question') DEFAULT 'neutral' COMMENT '评论类型',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (proposal_id) REFERENCES proposals(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_proposal (proposal_id),
    INDEX idx_user (user_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评论表';

-- Votes table - 投票表
CREATE TABLE IF NOT EXISTS votes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    proposal_id INT NOT NULL COMMENT '提案ID',
    user_id INT NOT NULL COMMENT '用户ID',
    vote_type ENUM('approve', 'reject', 'abstain') NOT NULL COMMENT '投票类型',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY unique_vote (proposal_id, user_id),
    FOREIGN KEY (proposal_id) REFERENCES proposals(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_proposal (proposal_id),
    INDEX idx_vote_type (vote_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='投票表';

-- Insert sample admin user (password: admin123)
INSERT INTO users (username, email, password_hash, real_name, role) VALUES 
('admin', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '管理员', 'admin')
ON DUPLICATE KEY UPDATE username = username;

-- Insert sample member user (password: member123)
INSERT INTO users (username, email, password_hash, real_name, role) VALUES 
('member', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '家庭成员', 'member')
ON DUPLICATE KEY UPDATE username = username;

-- Insert sample proposals
INSERT INTO proposals (title, description, solution, status, priority, creator_id) VALUES 
('购买新的家庭影院系统', '我们的电视音响系统已经用了很多年，音质不太好，建议升级家庭影院系统。', '预算2万元购买索尼或三星的家庭影院套装，包括4K电视和5.1声道音响。', 'pending', 'medium', 1),
('制定家庭作息时间表', '为了更好地协调家庭成员的时间，建议制定统一的作息时间表。', '周一到周五晚上10点后不看电视，周末可以适当放宽到11点。', 'draft', 'low', 2),
('安装家庭安防系统', '最近小区有入室盗窃案件，为了家庭安全考虑，建议安装智能安防系统。', '安装智能门锁、摄像头和报警系统，预算1万元左右。', 'approved', 'high', 1)
ON DUPLICATE KEY UPDATE title = title;

-- Insert sample comments
INSERT INTO comments (proposal_id, user_id, content, comment_type) VALUES 
(1, 2, '我觉得2万元的预算有点高，可以考虑1.5万元的方案。', 'oppose'),
(1, 1, '好的，我们可以再看看其他品牌的产品。', 'neutral'),
(2, 1, '这个建议很好，有助于提高家庭生活质量。', 'support'),
(3, 2, '安全第一，我支持这个提案。', 'support')
ON DUPLICATE KEY UPDATE content = content;

-- Insert sample votes
INSERT INTO votes (proposal_id, user_id, vote_type) VALUES 
(1, 1, 'approve'),
(1, 2, 'reject'),
(2, 1, 'approve'),
(2, 2, 'approve'),
(3, 1, 'approve'),
(3, 2, 'approve')
ON DUPLICATE KEY UPDATE vote_type = vote_type;
