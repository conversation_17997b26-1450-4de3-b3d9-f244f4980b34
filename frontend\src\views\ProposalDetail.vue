<template>
  <div class="proposal-detail-page">
    <!-- Loading State -->
    <div v-if="proposalStore.loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>

    <!-- Proposal Content -->
    <div v-else-if="proposalStore.currentProposal" class="proposal-content">
      <!-- Proposal Header -->
      <div class="proposal-header">
        <div class="header-content">
          <div class="header-info">
            <el-button @click="$router.go(-1)" class="back-button">
              <el-icon><ArrowLeft /></el-icon>
              返回
            </el-button>
            <div class="title-section">
              <h1>{{ proposal.title }}</h1>
              <div class="meta-info">
                <el-tag :type="getStatusTagType(proposal.status)">
                  {{ getStatusText(proposal.status) }}
                </el-tag>
                <el-tag :type="getPriorityTagType(proposal.priority)" size="small">
                  {{ getPriorityText(proposal.priority) }}
                </el-tag>
                <span class="creator">创建者：{{ proposal.creator_name }}</span>
                <span class="date">{{ formatDate(proposal.created_at) }}</span>
              </div>
            </div>
          </div>
          <div class="header-actions" v-if="canEdit">
            <el-button type="primary" @click="editProposal">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button type="success" @click="requestAIAdjudication" :loading="aiLoading">
              <el-icon><MagicStick /></el-icon>
              AI仲裁
            </el-button>
          </div>
        </div>
      </div>

      <el-row :gutter="24">
        <!-- Main Content -->
        <el-col :xs="24" :lg="16">
          <!-- Description -->
          <el-card class="content-card">
            <template #header>
              <h3>提案描述</h3>
            </template>
            <div class="description-content">
              <p>{{ proposal.description }}</p>
              <div v-if="proposal.expanded_description" class="expanded-section">
                <h4>AI扩展描述</h4>
                <p class="expanded-text">{{ proposal.expanded_description }}</p>
              </div>
            </div>
          </el-card>

          <!-- Solution -->
          <el-card v-if="proposal.solution" class="content-card">
            <template #header>
              <h3>解决方案</h3>
            </template>
            <div class="solution-content">
              <p>{{ proposal.solution }}</p>
              <div v-if="proposal.expanded_solution" class="expanded-section">
                <h4>AI扩展方案</h4>
                <p class="expanded-text">{{ proposal.expanded_solution }}</p>
              </div>
            </div>
          </el-card>

          <!-- AI Opinion -->
          <el-card v-if="proposal.ai_opinion" class="content-card ai-opinion">
            <template #header>
              <div class="ai-header">
                <el-icon><Avatar /></el-icon>
                <h3>AI仲裁意见</h3>
                <el-tag v-if="proposal.ai_approval_score" type="success">
                  评分: {{ proposal.ai_approval_score }}/10.00
                </el-tag>
              </div>
            </template>
            <div class="ai-content">
              <p>{{ proposal.ai_opinion }}</p>
            </div>
          </el-card>

          <!-- Comments Section -->
          <el-card class="content-card">
            <template #header>
              <div class="comments-header">
                <h3>讨论 ({{ comments.length }})</h3>
              </div>
            </template>
            
            <!-- Add Comment Form -->
            <div class="add-comment-form">
              <el-form @submit.prevent="addComment">
                <el-form-item>
                  <el-input
                    v-model="newComment.content"
                    type="textarea"
                    :rows="3"
                    placeholder="发表您的看法..."
                    maxlength="1000"
                    show-word-limit
                  />
                </el-form-item>
                <el-form-item>
                  <div class="comment-actions">
                    <el-select v-model="newComment.commentType" style="width: 120px;">
                      <el-option label="中性" value="neutral" />
                      <el-option label="支持" value="support" />
                      <el-option label="反对" value="oppose" />
                      <el-option label="疑问" value="question" />
                    </el-select>
                    <el-button 
                      type="primary" 
                      @click="addComment" 
                      :loading="commentLoading"
                      :disabled="!newComment.content.trim()"
                    >
                      发表评论
                    </el-button>
                  </div>
                </el-form-item>
              </el-form>
            </div>

            <!-- Comments List -->
            <div class="comments-list">
              <div v-for="comment in comments" :key="comment.id" class="comment-item">
                <div class="comment-header">
                  <div class="comment-user">
                    <el-avatar :size="32" :src="comment.avatar_url">
                      {{ comment.user_name?.charAt(0) }}
                    </el-avatar>
                    <div class="user-info">
                      <span class="username">{{ comment.user_name }}</span>
                      <span class="comment-date">{{ formatDate(comment.created_at) }}</span>
                    </div>
                  </div>
                  <el-tag :type="getCommentTagType(comment.comment_type)" size="small">
                    {{ getCommentTypeText(comment.comment_type) }}
                  </el-tag>
                </div>
                <div class="comment-content">
                  <p>{{ comment.content }}</p>
                  <div v-if="comment.expanded_content" class="expanded-section">
                    <h5>AI扩展内容</h5>
                    <p class="expanded-text">{{ comment.expanded_content }}</p>
                  </div>
                </div>
              </div>
              
              <el-empty v-if="comments.length === 0" description="暂无评论" />
            </div>
          </el-card>
        </el-col>

        <!-- Sidebar -->
        <el-col :xs="24" :lg="8">
          <!-- Voting -->
          <el-card class="sidebar-card">
            <template #header>
              <h3>投票表决</h3>
            </template>
            <div class="voting-section">
              <div class="vote-stats">
                <div class="stat-item approve">
                  <span class="count">{{ approveVotes }}</span>
                  <span class="label">赞成</span>
                </div>
                <div class="stat-item reject">
                  <span class="count">{{ rejectVotes }}</span>
                  <span class="label">反对</span>
                </div>
                <div class="stat-item abstain">
                  <span class="count">{{ abstainVotes }}</span>
                  <span class="label">弃权</span>
                </div>
              </div>
              
              <div class="vote-actions">
                <el-button 
                  type="success" 
                  @click="vote('approve')"
                  :loading="voteLoading"
                  class="vote-button"
                >
                  <el-icon><Select /></el-icon>
                  赞成
                </el-button>
                <el-button 
                  type="danger" 
                  @click="vote('reject')"
                  :loading="voteLoading"
                  class="vote-button"
                >
                  <el-icon><Close /></el-icon>
                  反对
                </el-button>
                <el-button 
                  @click="vote('abstain')"
                  :loading="voteLoading"
                  class="vote-button"
                >
                  <el-icon><Remove /></el-icon>
                  弃权
                </el-button>
              </div>
            </div>
          </el-card>

          <!-- Proposal Info -->
          <el-card class="sidebar-card">
            <template #header>
              <h3>提案信息</h3>
            </template>
            <div class="proposal-info">
              <div class="info-item">
                <span class="label">创建时间：</span>
                <span class="value">{{ formatDate(proposal.created_at) }}</span>
              </div>
              <div class="info-item" v-if="proposal.due_date">
                <span class="label">截止日期：</span>
                <span class="value">{{ formatDate(proposal.due_date) }}</span>
              </div>
              <div class="info-item" v-if="proposal.assigned_name">
                <span class="label">负责人：</span>
                <span class="value">{{ proposal.assigned_name }}</span>
              </div>
              <div class="info-item" v-if="proposal.completed_at">
                <span class="label">完成时间：</span>
                <span class="value">{{ formatDate(proposal.completed_at) }}</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- Error State -->
    <div v-else class="error-state">
      <el-empty description="提案不存在或已被删除">
        <el-button type="primary" @click="$router.push('/proposals')">
          返回提案列表
        </el-button>
      </el-empty>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  ArrowLeft,
  Edit,
  MagicStick,
  Avatar,
  Select,
  Close,
  Remove
} from '@element-plus/icons-vue'
import { useAuthStore } from '../stores/auth'
import { useProposalStore } from '../stores/proposal'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()
const proposalStore = useProposalStore()

const aiLoading = ref(false)
const commentLoading = ref(false)
const voteLoading = ref(false)

const newComment = ref({
  content: '',
  commentType: 'neutral'
})

const proposal = computed(() => proposalStore.currentProposal?.proposal)
const comments = computed(() => proposalStore.currentProposal?.comments || [])
const votes = computed(() => proposalStore.currentProposal?.votes || [])

const canEdit = computed(() => {
  return authStore.user?.id === proposal.value?.creator_id || authStore.isAdmin
})

const approveVotes = computed(() => {
  return votes.value.filter(v => v.vote_type === 'approve').length
})

const rejectVotes = computed(() => {
  return votes.value.filter(v => v.vote_type === 'reject').length
})

const abstainVotes = computed(() => {
  return votes.value.filter(v => v.vote_type === 'abstain').length
})

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const getStatusText = (status) => {
  const statusMap = {
    draft: '草稿',
    pending: '待处理',
    approved: '已通过',
    rejected: '已拒绝',
    completed: '已完成'
  }
  return statusMap[status] || status
}

const getStatusTagType = (status) => {
  const typeMap = {
    draft: 'info',
    pending: 'warning',
    approved: 'success',
    rejected: 'danger',
    completed: 'success'
  }
  return typeMap[status] || 'info'
}

const getPriorityText = (priority) => {
  const priorityMap = {
    low: '低',
    medium: '中',
    high: '高',
    urgent: '紧急'
  }
  return priorityMap[priority] || priority
}

const getPriorityTagType = (priority) => {
  const typeMap = {
    low: 'success',
    medium: 'warning',
    high: 'danger',
    urgent: 'danger'
  }
  return typeMap[priority] || 'info'
}

const getCommentTypeText = (type) => {
  const typeMap = {
    support: '支持',
    oppose: '反对',
    neutral: '中性',
    question: '疑问'
  }
  return typeMap[type] || type
}

const getCommentTagType = (type) => {
  const typeMap = {
    support: 'success',
    oppose: 'danger',
    neutral: 'info',
    question: 'warning'
  }
  return typeMap[type] || 'info'
}

const editProposal = () => {
  router.push(`/proposals/${route.params.id}/edit`)
}

const requestAIAdjudication = async () => {
  aiLoading.value = true
  try {
    const result = await proposalStore.requestAIAdjudication(route.params.id)
    if (result.success) {
      ElMessage.success('AI仲裁完成')
    } else {
      ElMessage.error(result.message)
    }
  } catch (error) {
    ElMessage.error('AI仲裁失败')
  } finally {
    aiLoading.value = false
  }
}

const addComment = async () => {
  if (!newComment.value.content.trim()) return

  commentLoading.value = true
  try {
    const result = await proposalStore.addComment(route.params.id, newComment.value)
    if (result.success) {
      ElMessage.success('评论发表成功')
      newComment.value = { content: '', commentType: 'neutral' }
    } else {
      ElMessage.error(result.message)
    }
  } catch (error) {
    ElMessage.error('评论发表失败')
  } finally {
    commentLoading.value = false
  }
}

const vote = async (voteType) => {
  voteLoading.value = true
  try {
    const result = await proposalStore.vote(route.params.id, { voteType })
    if (result.success) {
      ElMessage.success('投票成功')
    } else {
      ElMessage.error(result.message)
    }
  } catch (error) {
    ElMessage.error('投票失败')
  } finally {
    voteLoading.value = false
  }
}

onMounted(async () => {
  const result = await proposalStore.fetchProposal(route.params.id)
  if (!result.success) {
    ElMessage.error(result.message)
  }
})
</script>

<style scoped>
.proposal-detail-page {
  max-width: 1400px;
  margin: 0 auto;
}

.loading-container {
  padding: 40px;
}

.proposal-header {
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-info {
  flex: 1;
}

.back-button {
  margin-bottom: 16px;
}

.title-section h1 {
  font-size: 28px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 12px;
}

.meta-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.meta-info span {
  color: #718096;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.content-card {
  margin-bottom: 24px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.description-content,
.solution-content {
  line-height: 1.8;
}

.expanded-section {
  margin-top: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #667eea;
}

.expanded-section h4,
.expanded-section h5 {
  color: #667eea;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 600;
}

.expanded-text {
  color: #4a5568;
  line-height: 1.6;
}

.ai-opinion {
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
}

.ai-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.ai-content {
  line-height: 1.8;
  color: #2d3748;
}

.comments-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.add-comment-form {
  margin-bottom: 24px;
  padding-bottom: 24px;
  border-bottom: 1px solid #e2e8f0;
}

.comment-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.comments-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.comment-item {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.comment-user {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-info {
  display: flex;
  flex-direction: column;
}

.username {
  font-weight: 600;
  font-size: 14px;
  color: #2d3748;
}

.comment-date {
  font-size: 12px;
  color: #718096;
}

.comment-content {
  line-height: 1.6;
  color: #4a5568;
}

.sidebar-card {
  margin-bottom: 24px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.voting-section {
  text-align: center;
}

.vote-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  gap: 12px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  flex: 1;
  padding: 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.stat-item .count {
  font-size: 24px;
  font-weight: 700;
  color: #ffffff;
  padding: 8px 16px;
  border-radius: 6px;
  min-width: 40px;
  text-align: center;
}

.stat-item .label {
  font-size: 14px;
  font-weight: 500;
  color: #4a5568;
}

.stat-item.approve .count {
  background: #10b981;
  color: #ffffff;
}

.stat-item.reject .count {
  background: #ef4444;
  color: #ffffff;
}

.stat-item.abstain .count {
  background: #6b7280;
  color: #ffffff;
}

.vote-actions {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
}

.vote-button {
  flex: 1;
  min-width: 0;
}

.proposal-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e2e8f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-item .label {
  font-weight: 600;
  color: #4a5568;
}

.info-item .value {
  color: #718096;
}

.error-state {
  padding: 60px 20px;
  text-align: center;
}

@media (max-width: 768px) {
  .proposal-header {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
  }

  .title-section h1 {
    font-size: 24px;
  }

  .meta-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .header-actions {
    width: 100%;
    justify-content: stretch;
  }

  .header-actions .el-button {
    flex: 1;
  }

  .comment-actions {
    flex-direction: column;
    gap: 12px;
  }

  .comment-actions .el-select {
    width: 100%;
  }

  .vote-actions {
    flex-direction: row;
    gap: 8px;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style>
