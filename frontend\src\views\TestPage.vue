<template>
  <div>
    <h1>测试页面</h1>
    <p>计数器: {{ count }}</p>
    <button @click="increment" style="margin: 10px; padding: 10px;">增加</button>
    <button @click="testAlert" style="margin: 10px; padding: 10px;">测试弹窗</button>
    <button onclick="alert('原生点击')" style="margin: 10px; padding: 10px;">原生按钮</button>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const count = ref(0)

onMounted(() => {
  console.log('TestPage mounted')
})

const increment = () => {
  console.log('increment called')
  count.value++
}

const testAlert = () => {
  console.log('testAlert called')
  alert('测试弹窗')
}
</script>
