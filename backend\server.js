const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
require('dotenv').config();

const { testConnection, initDatabase } = require('./config/database');
const authRoutes = require('./routes/auth');
const proposalRoutes = require('./routes/proposals');
const llmRoutes = require('./routes/llm');
const userRoutes = require('./routes/users');

// 导入安全中间件
const {
  securityHeaders,
  corsOptions,
  apiRateLimit,
  requestSizeLimit,
  requestLogger,
  securityCheck
} = require('./middleware/security');

// 导入错误处理中间件
const {
  globalErrorHandler,
  notFoundHandler,
  handleUncaughtException,
  handleUnhandledRejection,
  handleGracefulShutdown
} = require('./middleware/errorHandler');

// 导入性能优化工具
const {
  compressionMiddleware,
  performanceMonitor
} = require('./utils/performance');

const app = express();
const PORT = process.env.PORT || 3000;

// 信任代理（如果使用反向代理）
app.set('trust proxy', 1);

// 性能优化中间件
app.use(compressionMiddleware);
app.use(performanceMonitor);

// 安全中间件
app.use(securityHeaders);
app.use(cors(corsOptions));
app.use(requestLogger);
app.use(securityCheck);
app.use(apiRateLimit);
app.use(requestSizeLimit);

// Body parsing middleware
app.use(bodyParser.json({ limit: '10mb' }));
app.use(bodyParser.urlencoded({ extended: true }));

// Routes
app.use('/api/auth', authRoutes.router);
app.use('/api/proposals', proposalRoutes);
app.use('/api/llm', llmRoutes);
app.use('/api/users', userRoutes);

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    message: '家庭办公平台后端运行正常',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// 404处理
app.use(notFoundHandler);

// 全局错误处理
app.use(globalErrorHandler);

// 设置异常处理
handleUncaughtException();
handleUnhandledRejection();

// Initialize database and start server
async function startServer() {
  try {
    await testConnection();
    await initDatabase();

    const server = app.listen(PORT, '0.0.0.0', () => {
      console.log(`家庭办公平台后端服务已启动，端口: ${PORT}`);
      console.log(`健康检查: http://localhost:${PORT}/health`);
      console.log(`环境: ${process.env.NODE_ENV || 'development'}`);
      console.log(`服务器绑定到: 0.0.0.0:${PORT} (允许外部访问)`);
    });

    // 设置优雅关闭
    handleGracefulShutdown(server);

  } catch (error) {
    console.error('服务器启动失败:', error);
    process.exit(1);
  }
}

startServer();

module.exports = app;
