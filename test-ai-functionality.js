const axios = require('axios');

async function testAIFunctionality() {
  try {
    console.log('🧪 测试AI功能状态...');
    
    // 1. 登录获取token
    console.log('\n1. 登录测试...');
    const loginResponse = await axios.post('http://localhost:3002/api/auth/login', {
      username: '<PERSON><PERSON><PERSON><PERSON>',
      password: '<PERSON><PERSON><PERSON><PERSON>'
    });
    
    const token = loginResponse.data.token;
    console.log('✅ 登录成功');
    
    // 2. 测试AI文本扩展功能
    console.log('\n2. 测试AI文本扩展...');
    try {
      const expandResponse = await axios.post('http://localhost:3002/api/llm/expand', {
        text: '我想改善家里的网络环境',
        type: 'description'
      }, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('✅ AI文本扩展成功');
      console.log('原文:', expandResponse.data.originalText);
      console.log('扩展结果:', expandResponse.data.expandedText.substring(0, 100) + '...');
    } catch (error) {
      console.log('❌ AI文本扩展失败:');
      console.log('状态码:', error.response?.status);
      console.log('错误信息:', error.response?.data);
      console.log('完整错误:', error.message);
    }
    
    // 3. 创建测试提案
    console.log('\n3. 创建测试提案...');
    const proposalResponse = await axios.post('http://localhost:3002/api/proposals', {
      title: 'AI功能测试提案',
      description: '这是一个用于测试AI功能的提案',
      solution: '通过自动化测试验证AI功能正常工作',
      priority: 'medium'
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    const proposalId = proposalResponse.data.proposalId;
    console.log('✅ 测试提案创建成功，ID:', proposalId);
    
    // 4. 测试AI仲裁功能
    console.log('\n4. 测试AI仲裁...');
    try {
      const adjudicationResponse = await axios.post(`http://localhost:3002/api/llm/adjudicate/${proposalId}`, {}, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('✅ AI仲裁成功');
      console.log('AI评分:', adjudicationResponse.data.aiScore);
      console.log('AI意见:', adjudicationResponse.data.aiOpinion.substring(0, 200) + '...');
    } catch (error) {
      console.log('❌ AI仲裁失败:');
      console.log('状态码:', error.response?.status);
      console.log('错误信息:', error.response?.data);
      console.log('完整错误:', error.message);
    }
    
    console.log('\n🎉 AI功能测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
  }
}

testAIFunctionality();
