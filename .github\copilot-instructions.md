<!-- Use this file to provide workspace-specific custom instructions to <PERSON><PERSON><PERSON>. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

# Family OA Platform - Copilot Instructions

This is a full-stack Family OA Platform project designed to make family communication more efficient.

## Technology Stack
- Frontend: Vue.js 3 with Element Plus UI library
- Backend: Node.js with Express.js
- Database: MySQL
- AI Integration: Google Gemini AI for content expansion and neutral adjudication

## Key Features
1. Proposal Workflow: Family members can propose needs, offer solutions, and record outcomes
2. LLM-Powered Expansion: AI expands and supplements user submissions
3. LLM as Neutral Adjudicator: AI evaluates proposals and provides unbiased opinions

## Code Guidelines
- All UI text must be in Chinese
- Follow minimalist, flat design principles
- Ensure multi-user concurrency support
- Use proper error handling and validation
- Follow RESTful API conventions
- Implement proper authentication and authorization
- Use environment variables for sensitive configuration

## Project Structure
- `/backend` - Express.js server with API endpoints
- `/frontend` - Vue.js application
- Database schemas support proposals, comments, and user management
- LLM integration for content enhancement and adjudication
