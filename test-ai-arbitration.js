const axios = require('axios');

async function testAIArbitration() {
  try {
    console.log('测试AI仲裁功能...');
    
    // 首先登录获取token
    const loginResponse = await axios.post('http://localhost:3002/api/auth/login', {
      username: 'l<PERSON><PERSON><PERSON>',
      password: 'lin<PERSON><PERSON>'
    });
    
    const token = loginResponse.data.token;
    console.log('登录成功，获取到token');
    
    // 创建一个测试提案
    const proposalResponse = await axios.post('http://localhost:3002/api/proposals', {
      title: '测试AI仲裁提案',
      description: '这是一个用于测试AI仲裁功能的提案',
      solution: '通过AI分析来评估提案的可行性',
      priority: 'medium'
    }, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    const proposalId = proposalResponse.data.proposalId;
    console.log('创建提案成功，ID:', proposalId);
    
    // 测试AI仲裁
    console.log('开始AI仲裁...');
    const arbitrationResponse = await axios.post(`http://localhost:3002/api/llm/adjudicate/${proposalId}`, {}, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log('AI仲裁成功!');
    console.log('AI意见:', arbitrationResponse.data.aiOpinion);
    console.log('AI评分:', arbitrationResponse.data.aiScore);
    
  } catch (error) {
    console.error('测试失败:');
    console.error('状态码:', error.response?.status);
    console.error('错误信息:', error.response?.data || error.message);
    console.error('完整错误:', error.response?.data);
  }
}

testAIArbitration();
