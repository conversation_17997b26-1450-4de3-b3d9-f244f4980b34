const mysql = require('mysql2/promise');
const bcrypt = require('bcryptjs');

async function updateUserSystem() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: '123456',
    database: 'family_oa_platform'
  });

  try {
    console.log('开始用户系统简化...');

    // 1. 备份现有用户数据
    console.log('1. 备份现有用户数据...');
    const [existingUsers] = await connection.execute('SELECT * FROM users');
    console.log(`备份了 ${existingUsers.length} 个用户`);

    // 2. 删除email和role字段
    console.log('2. 修改用户表结构...');
    
    try {
      await connection.execute('ALTER TABLE users DROP COLUMN email');
      console.log('✅ 删除email字段成功');
    } catch (error) {
      if (error.code !== 'ER_CANT_DROP_FIELD_OR_KEY') {
        console.log('⚠️ email字段可能已经不存在');
      }
    }

    try {
      await connection.execute('ALTER TABLE users DROP COLUMN role');
      console.log('✅ 删除role字段成功');
    } catch (error) {
      if (error.code !== 'ER_CANT_DROP_FIELD_OR_KEY') {
        console.log('⚠️ role字段可能已经不存在');
      }
    }

    // 3. 清空用户表
    console.log('3. 清空用户表...');
    await connection.execute('DELETE FROM users');
    await connection.execute('ALTER TABLE users AUTO_INCREMENT = 1');
    console.log('✅ 用户表已清空');

    // 4. 添加新用户
    console.log('4. 添加新用户...');
    
    const users = [
      { realName: '林凡皓', username: 'LinFanhao', password: 'LinFanhao' },
      { realName: '林卓恒', username: 'LinZhuoheng', password: 'LinZhuoheng' },
      { realName: '区敏', username: 'OuMin', password: 'OuMin' }
    ];

    for (const user of users) {
      const hashedPassword = await bcrypt.hash(user.password, 10);
      await connection.execute(
        'INSERT INTO users (username, password_hash, real_name) VALUES (?, ?, ?)',
        [user.username, hashedPassword, user.realName]
      );
      console.log(`✅ 添加用户: ${user.realName} (${user.username})`);
    }

    // 5. 验证新用户
    console.log('5. 验证新用户...');
    const [newUsers] = await connection.execute('SELECT id, username, real_name FROM users');
    console.log('新用户列表:');
    newUsers.forEach(user => {
      console.log(`  - ID: ${user.id}, 用户名: ${user.username}, 姓名: ${user.real_name}`);
    });

    console.log('\n🎉 用户系统简化完成！');
    console.log('📝 修改内容:');
    console.log('  - 移除了email字段');
    console.log('  - 移除了role字段（所有用户权限相同）');
    console.log('  - 重置了用户数据');
    console.log('  - 添加了3个新用户');

  } catch (error) {
    console.error('❌ 更新失败:', error);
  } finally {
    await connection.end();
  }
}

updateUserSystem();
