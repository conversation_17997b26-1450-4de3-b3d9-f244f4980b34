// 测试前端AI功能的脚本
const axios = require('axios');

async function testAIFunctionality() {
  try {
    console.log('开始测试AI功能...');
    
    // 1. 登录获取token
    console.log('1. 登录...');
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      username: 'admin',
      password: 'admin123'
    });
    
    const token = loginResponse.data.token;
    console.log('✓ 登录成功');
    
    // 2. 测试AI文本扩展
    console.log('2. 测试AI文本扩展...');
    const expandResponse = await axios.post('http://localhost:3001/api/llm/expand', {
      text: '我想改善家里的网络环境',
      type: 'description'
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✓ AI文本扩展成功');
    console.log('原文:', expandResponse.data.originalText);
    console.log('扩展结果:', expandResponse.data.expandedText.substring(0, 100) + '...');
    
    // 3. 创建一个测试提案
    console.log('3. 创建测试提案...');
    const proposalResponse = await axios.post('http://localhost:3001/api/proposals', {
      title: '测试AI功能提案',
      description: '这是一个用于测试AI功能的提案',
      solution: '通过自动化测试验证AI功能正常工作',
      priority: 'medium'
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    const proposalId = proposalResponse.data.proposalId;
    console.log('✓ 测试提案创建成功，ID:', proposalId);
    
    // 4. 测试AI仲裁
    console.log('4. 测试AI仲裁...');
    const adjudicationResponse = await axios.post(`http://localhost:3001/api/llm/adjudicate/${proposalId}`, {}, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✓ AI仲裁成功');
    console.log('AI评分:', adjudicationResponse.data.aiScore);
    console.log('AI意见:', adjudicationResponse.data.aiOpinion.substring(0, 100) + '...');
    
    console.log('\n🎉 所有AI功能测试通过！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
  }
}

testAIFunctionality();
