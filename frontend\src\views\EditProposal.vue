<template>
  <div class="edit-proposal-page">
    <!-- Page Header -->
    <div class="page-header">
      <div class="header-content">
        <h1>编辑提案</h1>
        <p>修改您的提案内容</p>
      </div>
      <div class="header-actions">
        <el-button @click="$router.go(-1)">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>

    <!-- Edit Form -->
    <el-card v-else class="form-card">
      <el-form
        ref="proposalFormRef"
        :model="proposalForm"
        :rules="proposalRules"
        label-width="100px"
        class="proposal-form"
      >
        <el-form-item label="提案标题" prop="title">
          <el-input
            v-model="proposalForm.title"
            placeholder="请输入提案标题"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="提案描述" prop="description">
          <el-input
            v-model="proposalForm.description"
            type="textarea"
            :rows="6"
            placeholder="请详细描述您的提案内容..."
            maxlength="2000"
            show-word-limit
          />
          <div class="ai-actions">
            <el-button
              size="small"
              type="primary"
              text
              :loading="expandingDescription"
              @click="expandDescription"
            >
              <el-icon><MagicStick /></el-icon>
              AI扩展描述
            </el-button>
          </div>
        </el-form-item>

        <el-form-item v-if="expandedDescription" label="AI扩展描述">
          <div class="expanded-content">
            <p>{{ expandedDescription }}</p>
            <div class="expanded-actions">
              <el-button size="small" @click="useExpandedDescription">
                使用此描述
              </el-button>
              <el-button size="small" @click="expandedDescription = ''">
                忽略
              </el-button>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="解决方案" prop="solution">
          <el-input
            v-model="proposalForm.solution"
            type="textarea"
            :rows="6"
            placeholder="请描述您的解决方案..."
            maxlength="2000"
            show-word-limit
          />
          <div class="ai-actions">
            <el-button
              size="small"
              type="primary"
              text
              :loading="expandingSolution"
              @click="expandSolution"
            >
              <el-icon><MagicStick /></el-icon>
              AI扩展方案
            </el-button>
          </div>
        </el-form-item>

        <el-form-item v-if="expandedSolution" label="AI扩展方案">
          <div class="expanded-content">
            <p>{{ expandedSolution }}</p>
            <div class="expanded-actions">
              <el-button size="small" @click="useExpandedSolution">
                使用此方案
              </el-button>
              <el-button size="small" @click="expandedSolution = ''">
                忽略
              </el-button>
            </div>
          </div>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="提案状态" prop="status">
              <el-select v-model="proposalForm.status" placeholder="选择状态">
                <el-option label="草稿" value="draft" />
                <el-option label="待处理" value="pending" />
                <el-option label="已通过" value="approved" />
                <el-option label="已拒绝" value="rejected" />
                <el-option label="已完成" value="completed" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="优先级" prop="priority">
              <el-select v-model="proposalForm.priority" placeholder="选择优先级">
                <el-option label="低" value="low" />
                <el-option label="中" value="medium" />
                <el-option label="高" value="high" />
                <el-option label="紧急" value="urgent" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="截止日期">
              <el-date-picker
                v-model="proposalForm.dueDate"
                type="date"
                placeholder="选择截止日期"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item v-if="authStore.isAdmin" label="分配给">
          <el-select v-model="proposalForm.assignedTo" placeholder="选择负责人" clearable>
            <el-option
              v-for="user in familyMembers"
              :key="user.id"
              :label="user.real_name"
              :value="user.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item>
          <div class="form-actions">
            <el-button @click="$router.go(-1)">
              取消
            </el-button>
            <el-button type="primary" @click="updateProposal" :loading="saving">
              保存修改
            </el-button>
          </div>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, MagicStick } from '@element-plus/icons-vue'
import { useAuthStore } from '../stores/auth'
import { useProposalStore } from '../stores/proposal'
import axios from 'axios'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()
const proposalStore = useProposalStore()

const proposalFormRef = ref()
const loading = ref(true)
const saving = ref(false)
const expandingDescription = ref(false)
const expandingSolution = ref(false)
const expandedDescription = ref('')
const expandedSolution = ref('')
const familyMembers = ref([])

const proposalForm = reactive({
  title: '',
  description: '',
  solution: '',
  status: 'draft',
  priority: 'medium',
  dueDate: null,
  assignedTo: null
})

const proposalRules = {
  title: [
    { required: true, message: '请输入提案标题', trigger: 'blur' },
    { min: 5, max: 200, message: '标题长度在5到200个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入提案描述', trigger: 'blur' },
    { min: 10, max: 2000, message: '描述长度在10到2000个字符', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ]
}

const expandDescription = async () => {
  if (!proposalForm.description.trim()) {
    ElMessage.warning('请先输入提案描述')
    return
  }

  expandingDescription.value = true
  try {
    const result = await proposalStore.expandContent(proposalForm.description, 'description')
    if (result.success) {
      expandedDescription.value = result.data.expandedText
    } else {
      ElMessage.error(result.message)
    }
  } catch (error) {
    ElMessage.error('AI扩展失败')
  } finally {
    expandingDescription.value = false
  }
}

const expandSolution = async () => {
  if (!proposalForm.solution.trim()) {
    ElMessage.warning('请先输入解决方案')
    return
  }

  expandingSolution.value = true
  try {
    const result = await proposalStore.expandContent(proposalForm.solution, 'solution')
    if (result.success) {
      expandedSolution.value = result.data.expandedText
    } else {
      ElMessage.error(result.message)
    }
  } catch (error) {
    ElMessage.error('AI扩展失败')
  } finally {
    expandingSolution.value = false
  }
}

const useExpandedDescription = () => {
  proposalForm.description = expandedDescription.value
  expandedDescription.value = ''
  ElMessage.success('已使用AI扩展描述')
}

const useExpandedSolution = () => {
  proposalForm.solution = expandedSolution.value
  expandedSolution.value = ''
  ElMessage.success('已使用AI扩展方案')
}

const updateProposal = async () => {
  if (!proposalFormRef.value) return

  const valid = await proposalFormRef.value.validate().catch(() => false)
  if (!valid) return

  saving.value = true
  try {
    const result = await proposalStore.updateProposal(route.params.id, proposalForm)

    if (result.success) {
      ElMessage.success('提案更新成功')
      router.push(`/proposals/${route.params.id}`)
    } else {
      ElMessage.error(result.message)
    }
  } catch (error) {
    ElMessage.error('更新失败')
  } finally {
    saving.value = false
  }
}

const loadFamilyMembers = async () => {
  try {
    const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api'
    const response = await axios.get(`${API_BASE_URL}/users/family`)
    familyMembers.value = response.data.users
  } catch (error) {
    console.error('加载家庭成员失败:', error)
  }
}

const loadProposal = async () => {
  try {
    const result = await proposalStore.fetchProposal(route.params.id)
    if (result.success) {
      const proposal = result.data.proposal
      
      // 检查权限
      if (proposal.creator_id !== authStore.user?.id && !authStore.isAdmin) {
        ElMessage.error('无权限编辑此提案')
        router.go(-1)
        return
      }

      // 填充表单
      proposalForm.title = proposal.title
      proposalForm.description = proposal.description
      proposalForm.solution = proposal.solution || ''
      proposalForm.status = proposal.status
      proposalForm.priority = proposal.priority
      proposalForm.dueDate = proposal.due_date
      proposalForm.assignedTo = proposal.assigned_to
    } else {
      ElMessage.error(result.message)
      router.go(-1)
    }
  } catch (error) {
    ElMessage.error('加载提案失败')
    router.go(-1)
  } finally {
    loading.value = false
  }
}

onMounted(async () => {
  await Promise.all([
    loadProposal(),
    loadFamilyMembers()
  ])
})
</script>

<style scoped>
.edit-proposal-page {
  max-width: 1000px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 24px;
}

.header-content h1 {
  font-size: 28px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 8px;
}

.header-content p {
  color: #718096;
  font-size: 16px;
}

.loading-container {
  padding: 40px;
}

.form-card {
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.proposal-form {
  max-width: 800px;
}

.ai-actions {
  margin-top: 8px;
  display: flex;
  gap: 8px;
}

.expanded-content {
  background: #f8f9fa;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 16px;
  margin-top: 8px;
}

.expanded-content p {
  margin-bottom: 12px;
  line-height: 1.6;
  color: #2d3748;
}

.expanded-actions {
  display: flex;
  gap: 8px;
}

.form-actions {
  display: flex;
  gap: 16px;
  padding-top: 16px;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-content h1 {
    font-size: 24px;
  }

  .proposal-form {
    max-width: 100%;
  }

  .form-actions {
    flex-direction: column;
  }

  .form-actions .el-button {
    width: 100%;
  }
}
</style>
