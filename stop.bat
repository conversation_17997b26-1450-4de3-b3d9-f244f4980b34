@echo off
chcp 65001 >nul
title FamilyOAPlatform 服务停止器

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                🛑 FamilyOAPlatform 服务停止器                  ║
echo ║                                                              ║
echo ║  正在停止以下服务：                                             ║
echo ║  • 前端Web服务 (端口: 5173)                                   ║
echo ║  • 后端API服务 (端口: 3000)                                   ║
echo ║  • MySQL 数据库服务                                           ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:: 设置日志文件
set LOG_FILE=logs\stop-%date:~0,4%%date:~5,2%%date:~8,2%-%time:~0,2%%time:~3,2%%time:~6,2%.log
if not exist "logs" mkdir logs
echo 服务停止时间: %date% %time% > "%LOG_FILE%"

echo [1/3] 停止前端服务...
echo [1/3] 停止前端服务... >> "%LOG_FILE%"

:: 查找并停止前端进程
for /f "tokens=2" %%a in ('tasklist /FI "WINDOWTITLE eq FamilyOA-Frontend*" /FO CSV /NH 2^>nul ^| findstr /V "INFO:"') do (
    if not "%%a"=="" (
        echo 正在停止前端进程 %%a...
        taskkill /PID %%a /F >nul 2>&1
        echo 前端进程 %%a 已停止 >> "%LOG_FILE%"
    )
)

:: 停止占用5173端口的进程
for /f "tokens=5" %%a in ('netstat -ano ^| find ":5173"') do (
    if not "%%a"=="" (
        echo 正在停止端口5173占用进程 %%a...
        taskkill /PID %%a /F >nul 2>&1
        echo 端口5173进程 %%a 已停止 >> "%LOG_FILE%"
    )
)

echo ✅ 前端服务已停止
echo.

echo [2/3] 停止后端服务...
echo [2/3] 停止后端服务... >> "%LOG_FILE%"

:: 查找并停止后端进程
for /f "tokens=2" %%a in ('tasklist /FI "WINDOWTITLE eq FamilyOA-Backend*" /FO CSV /NH 2^>nul ^| findstr /V "INFO:"') do (
    if not "%%a"=="" (
        echo 正在停止后端进程 %%a...
        taskkill /PID %%a /F >nul 2>&1
        echo 后端进程 %%a 已停止 >> "%LOG_FILE%"
    )
)

:: 停止占用3000端口的进程
for /f "tokens=5" %%a in ('netstat -ano ^| find ":3000"') do (
    if not "%%a"=="" (
        echo 正在停止端口3000占用进程 %%a...
        taskkill /PID %%a /F >nul 2>&1
        echo 端口3000进程 %%a 已停止 >> "%LOG_FILE%"
    )
)

echo ✅ 后端服务已停止
echo.

echo [3/3] 停止MySQL数据库服务...
echo [3/3] 停止MySQL数据库服务... >> "%LOG_FILE%"

:: 停止MySQL进程
tasklist /FI "IMAGENAME eq mysqld.exe" 2>NUL | find /I /N "mysqld.exe">NUL
if %errorLevel% equ 0 (
    echo 正在停止MySQL服务...
    taskkill /IM mysqld.exe /F >nul 2>&1
    
    :: 等待进程完全停止
    timeout /t 3 /nobreak >nul
    
    :: 验证是否停止成功
    tasklist /FI "IMAGENAME eq mysqld.exe" 2>NUL | find /I /N "mysqld.exe">NUL
    if %errorLevel% neq 0 (
        echo ✅ MySQL服务已停止
        echo MySQL服务已停止 >> "%LOG_FILE%"
    ) else (
        echo ⚠️  MySQL服务停止可能不完整
        echo MySQL服务停止不完整 >> "%LOG_FILE%"
    )
) else (
    echo ✅ MySQL服务未运行
    echo MySQL服务未运行 >> "%LOG_FILE%"
)

echo.
echo 服务停止完成时间: %date% %time% >> "%LOG_FILE%"

echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    ✅ 服务停止完成！                           ║
echo ║                                                              ║
echo ║  所有FamilyOAPlatform服务已停止                                ║
echo ║                                                              ║
echo ║  如需重新启动服务，请运行 start.bat                             ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

pause
