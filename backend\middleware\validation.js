const { body, param, query, validationResult } = require('express-validator');

// 通用验证错误处理中间件
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: '输入验证失败',
      details: errors.array().map(err => ({
        field: err.path,
        message: err.msg,
        value: err.value
      }))
    });
  }
  next();
};

// 用户注册验证规则
const validateUserRegistration = [
  body('username')
    .isLength({ min: 3, max: 50 })
    .withMessage('用户名长度必须在3-50个字符之间')
    .matches(/^[a-zA-Z0-9_\u4e00-\u9fa5]+$/)
    .withMessage('用户名只能包含字母、数字、下划线和中文字符'),

  body('password')
    .isLength({ min: 6, max: 128 })
    .withMessage('密码长度必须在6-128个字符之间'),

  body('realName')
    .isLength({ min: 2, max: 50 })
    .withMessage('真实姓名长度必须在2-50个字符之间')
    .matches(/^[\u4e00-\u9fa5a-zA-Z\s]+$/)
    .withMessage('真实姓名只能包含中文、英文字母和空格'),

  handleValidationErrors
];

// 用户登录验证规则
const validateUserLogin = [
  body('username')
    .notEmpty()
    .withMessage('用户名不能为空')
    .isLength({ max: 50 })
    .withMessage('用户名长度不能超过50个字符'),

  body('password')
    .notEmpty()
    .withMessage('密码不能为空')
    .isLength({ max: 128 })
    .withMessage('密码长度不能超过128个字符'),

  handleValidationErrors
];

// 提案创建验证规则
const validateProposalCreation = [
  body('title')
    .isLength({ min: 5, max: 200 })
    .withMessage('提案标题长度必须在5-200个字符之间')
    .trim(),
  
  body('description')
    .isLength({ min: 10, max: 5000 })
    .withMessage('提案描述长度必须在10-5000个字符之间')
    .trim(),
  
  body('solution')
    .optional()
    .isLength({ max: 5000 })
    .withMessage('解决方案长度不能超过5000个字符')
    .trim(),
  
  body('priority')
    .optional()
    .isIn(['low', 'medium', 'high', 'urgent'])
    .withMessage('优先级必须是 low、medium、high 或 urgent 之一'),
  
  body('dueDate')
    .optional()
    .isISO8601()
    .withMessage('截止日期格式不正确')
    .custom((value) => {
      if (new Date(value) <= new Date()) {
        throw new Error('截止日期必须是未来的日期');
      }
      return true;
    }),
  
  body('assignedTo')
    .optional()
    .isInt({ min: 1 })
    .withMessage('分配用户ID必须是正整数'),
  
  handleValidationErrors
];

// 提案更新验证规则
const validateProposalUpdate = [
  param('id')
    .isInt({ min: 1 })
    .withMessage('提案ID必须是正整数'),
  
  body('title')
    .optional()
    .isLength({ min: 5, max: 200 })
    .withMessage('提案标题长度必须在5-200个字符之间')
    .trim(),
  
  body('description')
    .optional()
    .isLength({ min: 10, max: 5000 })
    .withMessage('提案描述长度必须在10-5000个字符之间')
    .trim(),
  
  body('solution')
    .optional()
    .isLength({ max: 5000 })
    .withMessage('解决方案长度不能超过5000个字符')
    .trim(),
  
  body('status')
    .optional()
    .isIn(['draft', 'pending', 'approved', 'rejected', 'completed'])
    .withMessage('状态必须是有效的提案状态'),
  
  body('priority')
    .optional()
    .isIn(['low', 'medium', 'high', 'urgent'])
    .withMessage('优先级必须是 low、medium、high 或 urgent 之一'),
  
  handleValidationErrors
];

// 评论创建验证规则
const validateCommentCreation = [
  param('id')
    .isInt({ min: 1 })
    .withMessage('提案ID必须是正整数'),
  
  body('content')
    .isLength({ min: 1, max: 2000 })
    .withMessage('评论内容长度必须在1-2000个字符之间')
    .trim(),
  
  body('commentType')
    .optional()
    .isIn(['support', 'oppose', 'neutral', 'question'])
    .withMessage('评论类型必须是 support、oppose、neutral 或 question 之一'),
  
  handleValidationErrors
];

// 投票验证规则
const validateVote = [
  param('id')
    .isInt({ min: 1 })
    .withMessage('提案ID必须是正整数'),
  
  body('voteType')
    .isIn(['approve', 'reject', 'abstain'])
    .withMessage('投票类型必须是 approve、reject 或 abstain 之一'),
  
  handleValidationErrors
];

// LLM API验证规则
const validateLLMRequest = [
  body('text')
    .isLength({ min: 1, max: 10000 })
    .withMessage('文本长度必须在1-10000个字符之间')
    .trim(),
  
  body('type')
    .isIn(['description', 'solution'])
    .withMessage('类型必须是 description 或 solution'),
  
  handleValidationErrors
];

// ID参数验证
const validateId = [
  param('id')
    .isInt({ min: 1 })
    .withMessage('ID必须是正整数'),
  
  handleValidationErrors
];

// 分页参数验证
const validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1, max: 1000 })
    .withMessage('页码必须是1-1000之间的整数'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每页数量必须是1-100之间的整数'),
  
  query('sortBy')
    .optional()
    .isIn(['created_at', 'updated_at', 'title', 'priority', 'status'])
    .withMessage('排序字段无效'),
  
  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('排序顺序必须是 asc 或 desc'),
  
  handleValidationErrors
];

module.exports = {
  handleValidationErrors,
  validateUserRegistration,
  validateUserLogin,
  validateProposalCreation,
  validateProposalUpdate,
  validateCommentCreation,
  validateVote,
  validateLLMRequest,
  validateId,
  validatePagination
};
