const express = require('express');
const bcrypt = require('bcryptjs');
const { pool } = require('../config/database');
const { verifyToken } = require('./auth');
const router = express.Router();

// Get all users (all users can see this)
router.get('/', verifyToken, async (req, res) => {
  try {
    const [users] = await pool.execute(`
      SELECT id, username, real_name as nickname, avatar_url, created_at
      FROM users
      ORDER BY created_at DESC
    `);

    res.json(users);
  } catch (error) {
    console.error('获取用户列表错误:', error);
    res.status(500).json({ error: '获取用户列表失败' });
  }
});

// Create new user (all users can create)
router.post('/', verifyToken, async (req, res) => {
  try {
    const { username, nickname, password } = req.body;

    // Validate required fields
    if (!username || !nickname || !password) {
      return res.status(400).json({ error: '请填写所有必填字段' });
    }

    // Check if username already exists
    const [existingUsers] = await pool.execute(
      'SELECT id FROM users WHERE username = ?',
      [username]
    );

    if (existingUsers.length > 0) {
      return res.status(400).json({ error: '用户名已存在' });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Insert new user
    const [result] = await pool.execute(`
      INSERT INTO users (username, password_hash, real_name)
      VALUES (?, ?, ?)
    `, [username, hashedPassword, nickname]);

    res.status(201).json({
      message: '用户创建成功',
      userId: result.insertId
    });
  } catch (error) {
    console.error('创建用户错误:', error);
    res.status(500).json({ error: '创建用户失败' });
  }
});

// Update user (admin only)
router.put('/:id', verifyToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { nickname, email, role, is_active } = req.body;

    // Check if current user is admin
    const [currentUser] = await pool.execute(
      'SELECT role FROM users WHERE id = ?',
      [req.user.userId]
    );

    if (currentUser.length === 0 || currentUser[0].role !== 'admin') {
      return res.status(403).json({ error: '需要管理员权限' });
    }

    // Check if target user exists
    const [targetUser] = await pool.execute(
      'SELECT id, role FROM users WHERE id = ?',
      [id]
    );

    if (targetUser.length === 0) {
      return res.status(404).json({ error: '用户不存在' });
    }

    // Prevent admin from deactivating themselves
    if (parseInt(id) === req.user.userId && is_active === false) {
      return res.status(400).json({ error: '不能禁用自己的账户' });
    }

    // Check if email is already used by another user
    if (email) {
      const [existingUsers] = await pool.execute(
        'SELECT id FROM users WHERE email = ? AND id != ?',
        [email, id]
      );

      if (existingUsers.length > 0) {
        return res.status(400).json({ error: '邮箱已被其他用户使用' });
      }
    }

    // Build update query
    const updates = [];
    const params = [];

    if (nickname !== undefined) {
      updates.push('real_name = ?');
      params.push(nickname);
    }
    if (email !== undefined) {
      updates.push('email = ?');
      params.push(email);
    }
    if (role !== undefined && ['admin', 'member'].includes(role)) {
      updates.push('role = ?');
      params.push(role);
    }
    if (is_active !== undefined) {
      updates.push('is_active = ?');
      params.push(is_active ? 1 : 0);
    }

    if (updates.length === 0) {
      return res.status(400).json({ error: '没有提供要更新的字段' });
    }

    params.push(id);
    await pool.execute(
      `UPDATE users SET ${updates.join(', ')} WHERE id = ?`,
      params
    );

    res.json({ message: '用户信息更新成功' });
  } catch (error) {
    console.error('更新用户错误:', error);
    res.status(500).json({ error: '更新用户失败' });
  }
});

// Delete user (admin only)
router.delete('/:id', verifyToken, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if current user is admin
    const [currentUser] = await pool.execute(
      'SELECT role FROM users WHERE id = ?',
      [req.user.userId]
    );

    if (currentUser.length === 0 || currentUser[0].role !== 'admin') {
      return res.status(403).json({ error: '需要管理员权限' });
    }

    // Prevent admin from deleting themselves
    if (parseInt(id) === req.user.userId) {
      return res.status(400).json({ error: '不能删除自己的账户' });
    }

    // Check if user exists
    const [targetUser] = await pool.execute(
      'SELECT id, role FROM users WHERE id = ?',
      [id]
    );

    if (targetUser.length === 0) {
      return res.status(404).json({ error: '用户不存在' });
    }

    // Delete user (this will cascade delete related records due to foreign key constraints)
    await pool.execute('DELETE FROM users WHERE id = ?', [id]);

    res.json({ message: '用户删除成功' });
  } catch (error) {
    console.error('删除用户错误:', error);
    res.status(500).json({ error: '删除用户失败' });
  }
});

// Get family members (all users can see this)
router.get('/family', verifyToken, async (req, res) => {
  try {
    const [users] = await pool.execute(`
      SELECT id, username, real_name, avatar_url
      FROM users
      ORDER BY real_name ASC
    `);

    res.json({ users });
  } catch (error) {
    console.error('获取家庭成员列表错误:', error);
    res.status(500).json({ error: '获取家庭成员列表失败' });
  }
});

// Update user profile
router.put('/profile', verifyToken, async (req, res) => {
  try {
    const { realName, avatarUrl } = req.body;

    // Build update query dynamically
    const updates = [];
    const params = [];

    if (realName !== undefined) {
      updates.push('real_name = ?');
      params.push(realName);
    }
    if (avatarUrl !== undefined) {
      updates.push('avatar_url = ?');
      params.push(avatarUrl);
    }

    if (updates.length === 0) {
      return res.status(400).json({ error: '没有提供要更新的字段' });
    }

    params.push(req.user.userId);
    await pool.execute(
      `UPDATE users SET ${updates.join(', ')} WHERE id = ?`,
      params
    );

    res.json({ message: '用户资料更新成功' });
  } catch (error) {
    console.error('更新用户资料错误:', error);
    res.status(500).json({ error: '更新用户资料失败' });
  }
});

// Get user statistics
router.get('/stats', verifyToken, async (req, res) => {
  try {
    // Get proposal statistics
    const [proposalStats] = await pool.execute(`
      SELECT 
        COUNT(*) as total_proposals,
        COUNT(CASE WHEN status = 'draft' THEN 1 END) as draft_proposals,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_proposals,
        COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_proposals,
        COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_proposals,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_proposals
      FROM proposals
      WHERE creator_id = ?
    `, [req.user.userId]);

    // Get comment statistics
    const [commentStats] = await pool.execute(`
      SELECT COUNT(*) as total_comments
      FROM comments
      WHERE user_id = ?
    `, [req.user.userId]);

    // Get vote statistics
    const [voteStats] = await pool.execute(`
      SELECT 
        COUNT(*) as total_votes,
        COUNT(CASE WHEN vote_type = 'approve' THEN 1 END) as approve_votes,
        COUNT(CASE WHEN vote_type = 'reject' THEN 1 END) as reject_votes,
        COUNT(CASE WHEN vote_type = 'abstain' THEN 1 END) as abstain_votes
      FROM votes
      WHERE user_id = ?
    `, [req.user.userId]);

    res.json({
      proposals: proposalStats[0],
      comments: commentStats[0],
      votes: voteStats[0]
    });
  } catch (error) {
    console.error('获取用户统计信息错误:', error);
    res.status(500).json({ error: '获取用户统计信息失败' });
  }
});

module.exports = router;
