const express = require('express');
const { GoogleGenerativeAI } = require('@google/generative-ai');
const { pool } = require('../config/database');
const { verifyToken } = require('./auth');
const router = express.Router();

// Initialize Google AI
const genAI = new GoogleGenerativeAI(process.env.GOOGLE_AI_API_KEY);

// Expand content using LLM
router.post('/expand', verifyToken, async (req, res) => {
  try {
    const { text, type = 'description' } = req.body;

    if (!text) {
      return res.status(400).json({ error: '需要扩展的文本不能为空' });
    }

    const model = genAI.getGenerativeModel({ model: process.env.GOOGLE_AI_MODEL || 'gemini-2.5-flash' });

    let prompt;
    if (type === 'description') {
      prompt = `作为家庭事务助手，请帮助扩展以下提案描述，使其更加清晰具体：

【原始描述】${text}

【扩展要求】
请按以下结构输出，每部分简洁明了：

**问题背景**
- 简述当前情况和存在的问题

**影响分析**
- 对家庭成员的具体影响
- 不解决可能产生的后果

**预期效果**
- 解决后的改善情况
- 对家庭生活的积极影响

【输出要求】
- 保持原意，增加细节
- 语言简洁，逻辑清晰
- 总字数150-250字
- 使用中文回复`;
    } else if (type === 'solution') {
      prompt = `作为家庭事务助手，请帮助完善以下解决方案，使其更加可操作：

【原始方案】${text}

【完善要求】
请按以下结构输出：

**实施步骤**
1. 第一步：具体行动
2. 第二步：具体行动
3. 第三步：具体行动

**资源需求**
- 所需时间、人力、物力

**注意事项**
- 可能遇到的困难
- 应对措施

**时间安排**
- 预计完成时间

【输出要求】
- 步骤具体可操作
- 考虑实际可行性
- 总字数200-350字
- 使用中文回复`;
    } else if (type === 'comment') {
      prompt = `作为家庭事务助手，请帮助完善以下评论，使其更有建设性：

【原始评论】${text}

【完善要求】
请按以下结构输出：

**观点表达**
- 明确表达支持/反对/中立立场

**理由说明**
- 具体的支撑论据
- 基于家庭实际情况的分析

**建议补充**
- 改进建议或替代方案
- 实施建议

【输出要求】
- 保持原有态度和观点
- 语言友好尊重
- 提供建设性意见
- 总字数150-250字
- 使用中文回复`;
    }

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const expandedText = response.text();

    res.json({
      originalText: text,
      expandedText: expandedText,
      type: type
    });
  } catch (error) {
    console.error('文本扩展错误:', error);
    res.status(500).json({ error: '文本扩展失败，请稍后重试' });
  }
});

// AI adjudication for proposal
router.post('/adjudicate/:proposalId', verifyToken, async (req, res) => {
  try {
    const { proposalId } = req.params;

    // Get proposal details with comments and votes
    const [proposals] = await pool.execute(`
      SELECT p.*, u.real_name as creator_name
      FROM proposals p
      LEFT JOIN users u ON p.creator_id = u.id
      WHERE p.id = ?
    `, [proposalId]);

    if (proposals.length === 0) {
      return res.status(404).json({ error: '提案不存在' });
    }

    const proposal = proposals[0];

    // Get comments
    const [comments] = await pool.execute(`
      SELECT c.content, c.comment_type, u.real_name as user_name
      FROM comments c
      LEFT JOIN users u ON c.user_id = u.id
      WHERE c.proposal_id = ?
      ORDER BY c.created_at ASC
    `, [proposalId]);

    // Get votes
    const [votes] = await pool.execute(`
      SELECT vote_type, COUNT(*) as count
      FROM votes
      WHERE proposal_id = ?
      GROUP BY vote_type
    `, [proposalId]);

    const model = genAI.getGenerativeModel({ model: process.env.GOOGLE_AI_MODEL || 'gemini-2.5-flash' });

    const prompt = `作为中立的家庭事务仲裁者，请对以下提案进行客观评估：

【提案信息】
标题：${proposal.title}
描述：${proposal.description}
解决方案：${proposal.solution || '未提供具体解决方案'}
提案人：${proposal.creator_name}
优先级：${proposal.priority}

【家庭成员反馈】
${comments.length > 0 ? comments.map(c => `${c.user_name}（${c.comment_type}）：${c.content}`).join('\n') : '暂无评论'}

【投票统计】
${votes.length > 0 ? votes.map(v => `${v.vote_type === 'approve' ? '赞成' : v.vote_type === 'reject' ? '反对' : '弃权'}: ${v.count}票`).join('\n') : '暂无投票'}

【评估要求】
请按以下结构输出：

**综合评估**
- 提案的可行性分析
- 对家庭的影响评估
- 实施难度评价

**改进建议**
- 具体的优化方向
- 实施建议

**推荐评分**
评分：X.XX/10.00
（评分标准：可行性30%、必要性30%、家庭和谐20%、资源投入20%）

【输出要求】
- 保持中立客观
- 语言简洁清晰
- 总字数250-400字
- 使用中文回复`;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const aiOpinion = response.text();

    // Extract score from AI response (improved regex matching)
    const scoreMatch = aiOpinion.match(/评分[：:]\s*(\d+\.\d{2})/i) || aiOpinion.match(/(\d+\.\d{2})\/10/);
    const aiScore = scoreMatch ? parseFloat(scoreMatch[1]) : null;

    // Update proposal with AI opinion and score
    await pool.execute(`
      UPDATE proposals 
      SET ai_opinion = ?, ai_approval_score = ?
      WHERE id = ?
    `, [aiOpinion, aiScore, proposalId]);

    res.json({
      aiOpinion,
      aiScore,
      message: 'AI仲裁完成'
    });
  } catch (error) {
    console.error('AI仲裁错误:', error);
    res.status(500).json({ error: 'AI仲裁失败，请稍后重试' });
  }
});

// Update proposal with expanded content
router.post('/update-expanded/:proposalId', verifyToken, async (req, res) => {
  try {
    const { proposalId } = req.params;
    const { expandedDescription, expandedSolution } = req.body;

    // Check if user owns the proposal or is admin
    const [proposals] = await pool.execute(
      'SELECT creator_id FROM proposals WHERE id = ?',
      [proposalId]
    );

    if (proposals.length === 0) {
      return res.status(404).json({ error: '提案不存在' });
    }

    // 简化权限检查：只允许提案创建者修改
    if (proposals[0].creator_id !== req.user.userId) {
      return res.status(403).json({ error: '只有提案创建者可以修改此提案' });
    }

    // Update expanded content
    const updates = [];
    const params = [];

    if (expandedDescription) {
      updates.push('expanded_description = ?');
      params.push(expandedDescription);
    }
    if (expandedSolution) {
      updates.push('expanded_solution = ?');
      params.push(expandedSolution);
    }

    if (updates.length === 0) {
      return res.status(400).json({ error: '没有提供要更新的扩展内容' });
    }

    params.push(proposalId);
    await pool.execute(
      `UPDATE proposals SET ${updates.join(', ')} WHERE id = ?`,
      params
    );

    res.json({ message: '扩展内容更新成功' });
  } catch (error) {
    console.error('更新扩展内容错误:', error);
    res.status(500).json({ error: '更新扩展内容失败' });
  }
});

// Update comment with expanded content
router.post('/update-expanded-comment/:commentId', verifyToken, async (req, res) => {
  try {
    const { commentId } = req.params;
    const { expandedContent } = req.body;

    if (!expandedContent) {
      return res.status(400).json({ error: '扩展内容不能为空' });
    }

    // Check if user owns the comment or is admin
    const [comments] = await pool.execute(
      'SELECT user_id FROM comments WHERE id = ?',
      [commentId]
    );

    if (comments.length === 0) {
      return res.status(404).json({ error: '评论不存在' });
    }

    // 简化权限检查：只允许评论创建者修改
    if (comments[0].user_id !== req.user.userId) {
      return res.status(403).json({ error: '只有评论创建者可以修改此评论' });
    }

    await pool.execute(
      'UPDATE comments SET expanded_content = ? WHERE id = ?',
      [expandedContent, commentId]
    );

    res.json({ message: '评论扩展内容更新成功' });
  } catch (error) {
    console.error('更新评论扩展内容错误:', error);
    res.status(500).json({ error: '更新评论扩展内容失败' });
  }
});

module.exports = router;
