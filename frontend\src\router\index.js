import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '../stores/auth'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      redirect: '/dashboard'
    },
    {
      path: '/login',
      name: 'Login',
      component: () => import('../views/Login.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/register',
      name: 'Register',
      component: () => import('../views/Register.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/test',
      name: 'Test',
      component: () => import('../views/TestPage.vue')
    },
    {
      path: '/simple-login',
      name: 'SimpleLogin',
      component: () => import('../views/SimpleLogin.vue')
    },    {
      path: '/',
      component: () => import('../components/Layout.vue'),
      meta: { requiresAuth: true },
      children: [
        {
          path: 'dashboard',
          name: 'Dashboard',
          component: () => import('../views/Dashboard.vue')
        },
        {
          path: 'proposals',
          name: 'Proposals',
          component: () => import('../views/Proposals.vue')
        },
        {
          path: 'proposals/create',
          name: 'CreateProposal',
          component: () => import('../views/CreateProposal.vue')
        },
        {
          path: 'proposals/:id',
          name: 'ProposalDetail',
          component: () => import('../views/ProposalDetail.vue')
        },
        {
          path: 'proposals/:id/edit',
          name: 'EditProposal',
          component: () => import('../views/EditProposal.vue')
        },
        {
          path: 'profile',
          name: 'Profile',
          component: () => import('../views/Profile.vue')
        },
        {
          path: 'users',
          name: 'Users',
          component: () => import('../views/Users.vue')
        }
      ]
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      component: () => import('../views/NotFound.vue')
    }
  ]
})

// Navigation guards
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  
  // Check if user is authenticated
  if (!authStore.isAuthenticated && authStore.token) {
    try {
      await authStore.fetchUser()
    } catch (error) {
      authStore.logout()
    }
  }

  // Handle routes that require authentication
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next('/login')
    return
  }

  // Handle routes that require guest access (login/register)
  if (to.meta.requiresGuest && authStore.isAuthenticated) {
    next('/dashboard')
    return
  }

  // 移除管理员权限检查，所有用户权限相同

  next()
})

export default router
