const { pool } = require('./config/database');

async function testProposalAPI() {
  try {
    console.log('Testing proposal detail API...');
    
    // First, get a proposal ID from the database
    const [proposals] = await pool.execute('SELECT id FROM proposals LIMIT 1');
    
    if (proposals.length === 0) {
      console.log('No proposals found in database');
      return;
    }
    
    const proposalId = proposals[0].id;
    console.log('Testing with proposal ID:', proposalId);
    
    // Test the actual API query
    const [result] = await pool.execute(`
      SELECT p.*, 
             u.real_name as creator_name,
             assigned_user.real_name as assigned_name
      FROM proposals p
      LEFT JOIN users u ON p.creator_id = u.id
      LEFT JOIN users assigned_user ON p.assigned_to = assigned_user.id
      WHERE p.id = ?
    `, [proposalId]);
    
    if (result.length === 0) {
      console.log('No proposal found with ID:', proposalId);
    } else {
      console.log('Proposal found:', {
        id: result[0].id,
        title: result[0].title,
        creator_name: result[0].creator_name
      });
    }
    
    // Test comments query
    const [comments] = await pool.execute(`
      SELECT c.*, u.real_name as user_name, u.avatar_url
      FROM comments c
      LEFT JOIN users u ON c.user_id = u.id
      WHERE c.proposal_id = ?
      ORDER BY c.created_at ASC
    `, [proposalId]);
    
    console.log('Comments found:', comments.length);
    
    // Test votes query
    const [votes] = await pool.execute(`
      SELECT v.*, u.real_name as user_name
      FROM votes v
      LEFT JOIN users u ON v.user_id = u.id
      WHERE v.proposal_id = ?
    `, [proposalId]);
    
    console.log('Votes found:', votes.length);
    
  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    process.exit(0);
  }
}

testProposalAPI();
