<template>
  <el-card 
    class="dashboard-card modern-card hover-lift"
    :class="[`card-${type}`, { 'card-loading': loading }]"
    shadow="hover"
  >
    <template #header>
      <div class="card-header">
        <div class="header-content">
          <div class="icon-wrapper" :class="`icon-${type}`">
            <el-icon :size="24">
              <component :is="icon" />
            </el-icon>
          </div>
          <div class="header-text">
            <h3 class="card-title">{{ title }}</h3>
            <p class="card-subtitle">{{ subtitle }}</p>
          </div>
        </div>
        <div v-if="showTrend" class="trend-indicator" :class="trendClass">
          <el-icon :size="16">
            <component :is="trendIcon" />
          </el-icon>
          <span class="trend-text">{{ trendText }}</span>
        </div>
      </div>
    </template>

    <div class="card-content">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-content">
        <div class="skeleton skeleton-line"></div>
        <div class="skeleton skeleton-line short"></div>
        <div class="skeleton skeleton-line"></div>
      </div>
      
      <!-- 主要内容 -->
      <div v-else class="main-content">
        <div class="primary-metric">
          <span class="metric-value" :class="`value-${type}`">{{ value }}</span>
          <span class="metric-unit">{{ unit }}</span>
        </div>
        
        <div v-if="description" class="metric-description">
          {{ description }}
        </div>
        
        <!-- 次要指标 -->
        <div v-if="secondaryMetrics && secondaryMetrics.length" class="secondary-metrics">
          <div 
            v-for="metric in secondaryMetrics" 
            :key="metric.label"
            class="secondary-metric"
          >
            <span class="metric-label">{{ metric.label }}</span>
            <span class="metric-value-small">{{ metric.value }}</span>
          </div>
        </div>
        
        <!-- 进度条 -->
        <div v-if="showProgress" class="progress-section">
          <div class="progress-header">
            <span class="progress-label">{{ progressLabel }}</span>
            <span class="progress-percentage">{{ progressPercentage }}%</span>
          </div>
          <el-progress 
            :percentage="progressPercentage" 
            :color="progressColor"
            :stroke-width="6"
            :show-text="false"
          />
        </div>
      </div>
    </div>

    <!-- 底部操作区 -->
    <template v-if="showActions" #footer>
      <div class="card-actions">
        <el-button 
          v-for="action in actions" 
          :key="action.label"
          :type="action.type || 'text'"
          :icon="action.icon"
          size="small"
          @click="$emit('action', action.key)"
        >
          {{ action.label }}
        </el-button>
      </div>
    </template>
  </el-card>
</template>

<script setup>
import { computed } from 'vue'
import { 
  TrendChartUp, 
  TrendChartDown, 
  Minus 
} from '@element-plus/icons-vue'

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  subtitle: {
    type: String,
    default: ''
  },
  value: {
    type: [String, Number],
    required: true
  },
  unit: {
    type: String,
    default: ''
  },
  description: {
    type: String,
    default: ''
  },
  icon: {
    type: [String, Object],
    required: true
  },
  type: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'primary', 'success', 'warning', 'danger', 'info'].includes(value)
  },
  loading: {
    type: Boolean,
    default: false
  },
  showTrend: {
    type: Boolean,
    default: false
  },
  trend: {
    type: String,
    default: 'neutral',
    validator: (value) => ['up', 'down', 'neutral'].includes(value)
  },
  trendValue: {
    type: [String, Number],
    default: ''
  },
  secondaryMetrics: {
    type: Array,
    default: () => []
  },
  showProgress: {
    type: Boolean,
    default: false
  },
  progressLabel: {
    type: String,
    default: '进度'
  },
  progressPercentage: {
    type: Number,
    default: 0
  },
  progressColor: {
    type: String,
    default: '#409eff'
  },
  showActions: {
    type: Boolean,
    default: false
  },
  actions: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['action'])

// 计算属性
const trendClass = computed(() => {
  return {
    'trend-up': props.trend === 'up',
    'trend-down': props.trend === 'down',
    'trend-neutral': props.trend === 'neutral'
  }
})

const trendIcon = computed(() => {
  switch (props.trend) {
    case 'up':
      return TrendChartUp
    case 'down':
      return TrendChartDown
    default:
      return Minus
  }
})

const trendText = computed(() => {
  if (!props.trendValue) return ''
  const prefix = props.trend === 'up' ? '+' : props.trend === 'down' ? '-' : ''
  return `${prefix}${props.trendValue}`
})
</script>

<style scoped>
.dashboard-card {
  transition: all 0.3s ease;
  border: none;
  border-radius: 12px;
  overflow: hidden;
}

.dashboard-card.card-loading {
  pointer-events: none;
}

/* 卡片类型样式 */
.card-primary {
  border-left: 4px solid var(--primary-color, #409eff);
}

.card-success {
  border-left: 4px solid var(--success-color, #67c23a);
}

.card-warning {
  border-left: 4px solid var(--warning-color, #e6a23c);
}

.card-danger {
  border-left: 4px solid var(--danger-color, #f56c6c);
}

.card-info {
  border-left: 4px solid var(--info-color, #909399);
}

/* 头部样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin: -8px 0;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.icon-wrapper {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.icon-primary { background: linear-gradient(135deg, #409eff, #337ecc); }
.icon-success { background: linear-gradient(135deg, #67c23a, #529b2e); }
.icon-warning { background: linear-gradient(135deg, #e6a23c, #b88230); }
.icon-danger { background: linear-gradient(135deg, #f56c6c, #c45656); }
.icon-info { background: linear-gradient(135deg, #909399, #73767a); }
.icon-default { background: linear-gradient(135deg, #ddd, #bbb); }

.header-text {
  flex: 1;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary, #303133);
  margin: 0 0 4px 0;
}

.card-subtitle {
  font-size: 14px;
  color: var(--text-secondary, #606266);
  margin: 0;
}

/* 趋势指示器 */
.trend-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
}

.trend-up {
  background: #f0f9ff;
  color: #0369a1;
}

.trend-down {
  background: #fef2f2;
  color: #dc2626;
}

.trend-neutral {
  background: #f9fafb;
  color: #6b7280;
}

/* 内容样式 */
.card-content {
  padding: 8px 0;
}

.loading-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.skeleton-line {
  height: 16px;
  border-radius: 4px;
}

.skeleton-line.short {
  width: 60%;
}

.main-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.primary-metric {
  display: flex;
  align-items: baseline;
  gap: 8px;
}

.metric-value {
  font-size: 32px;
  font-weight: 700;
  line-height: 1;
}

.value-primary { color: var(--primary-color, #409eff); }
.value-success { color: var(--success-color, #67c23a); }
.value-warning { color: var(--warning-color, #e6a23c); }
.value-danger { color: var(--danger-color, #f56c6c); }
.value-info { color: var(--info-color, #909399); }
.value-default { color: var(--text-primary, #303133); }

.metric-unit {
  font-size: 16px;
  color: var(--text-secondary, #606266);
  font-weight: 500;
}

.metric-description {
  font-size: 14px;
  color: var(--text-secondary, #606266);
  line-height: 1.5;
}

/* 次要指标 */
.secondary-metrics {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.secondary-metric {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.metric-label {
  font-size: 12px;
  color: var(--text-tertiary, #909399);
  font-weight: 500;
}

.metric-value-small {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary, #303133);
}

/* 进度条 */
.progress-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-label {
  font-size: 14px;
  color: var(--text-secondary, #606266);
  font-weight: 500;
}

.progress-percentage {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary, #303133);
}

/* 操作区 */
.card-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  margin: -8px 0;
}

/* 响应式 */
@media (max-width: 768px) {
  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .trend-indicator {
    align-self: flex-end;
  }
  
  .secondary-metrics {
    flex-direction: column;
    gap: 12px;
  }
}
</style>
