@echo off
chcp 65001 >nul
title FamilyOAPlatform 一键安装向导

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                🚀 FamilyOAPlatform 快速安装向导                ║
echo ║                                                              ║
echo ║  本脚本将自动完成以下操作：                                      ║
echo ║  • 检查系统环境                                                ║
echo ║  • 安装Node.js 18.x LTS                                      ║
echo ║  • 配置MySQL 8.0便携版                                        ║
echo ║  • 安装项目依赖                                                ║
echo ║  • 初始化数据库                                                ║
echo ║  • 生成配置文件                                                ║
echo ║                                                              ║
echo ║  预计用时：3-5分钟                                             ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ 错误：需要管理员权限
    echo.
    echo 请右键点击此脚本，选择"以管理员身份运行"
    echo.
    pause
    exit /b 1
)

:: 创建必要目录
if not exist "scripts" mkdir scripts
if not exist "logs" mkdir logs
if not exist "mysql" mkdir mysql
if not exist "temp" mkdir temp

:: 设置日志文件
set LOG_FILE=logs\install-%date:~0,4%%date:~5,2%%date:~8,2%-%time:~0,2%%time:~3,2%%time:~6,2%.log
echo 安装开始时间: %date% %time% > "%LOG_FILE%"

echo [1/6] 检查系统环境...
echo [1/6] 检查系统环境... >> "%LOG_FILE%"

:: 检查Windows版本
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo 系统版本: %VERSION% >> "%LOG_FILE%"

:: 检查可用内存
for /f "skip=1" %%p in ('wmic computersystem get TotalPhysicalMemory') do (
    set /a MEMORY_GB=%%p/1024/1024/1024
    goto :memory_done
)
:memory_done
echo 系统内存: %MEMORY_GB%GB >> "%LOG_FILE%"

if %MEMORY_GB% LSS 4 (
    echo ⚠️  警告：系统内存少于4GB，可能影响性能
    echo 系统内存少于4GB >> "%LOG_FILE%"
)

:: 检查磁盘空间
for /f "tokens=3" %%a in ('dir /-c ^| find "bytes free"') do set DISK_FREE=%%a
set /a DISK_FREE_GB=%DISK_FREE:~0,-9%/1024/1024/1024
echo 可用磁盘空间: %DISK_FREE_GB%GB >> "%LOG_FILE%"

if %DISK_FREE_GB% LSS 2 (
    echo ❌ 错误：磁盘空间不足，至少需要2GB可用空间
    echo 磁盘空间不足 >> "%LOG_FILE%"
    pause
    exit /b 1
)

echo ✅ 系统环境检查通过
echo.

echo [2/6] 检查并安装Node.js...
echo [2/6] 检查并安装Node.js... >> "%LOG_FILE%"

:: 检查Node.js是否已安装
node --version >nul 2>&1
if %errorLevel% equ 0 (
    for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
    echo 检测到Node.js版本: %NODE_VERSION%
    echo 检测到Node.js版本: %NODE_VERSION% >> "%LOG_FILE%"
    
    :: 检查版本是否符合要求（v18或更高）
    set NODE_MAJOR=%NODE_VERSION:~1,2%
    if %NODE_MAJOR% GEQ 18 (
        echo ✅ Node.js版本符合要求
        goto :nodejs_done
    ) else (
        echo ⚠️  Node.js版本过低，需要升级到v18+
    )
) else (
    echo 未检测到Node.js，开始下载安装...
    echo 未检测到Node.js >> "%LOG_FILE%"
)

:: 下载并安装Node.js
echo 正在下载Node.js 18.19.0 LTS...
set NODEJS_URL=https://nodejs.org/dist/v18.19.0/node-v18.19.0-x64.msi
set NODEJS_FILE=temp\nodejs-installer.msi

powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri '%NODEJS_URL%' -OutFile '%NODEJS_FILE%'}" >> "%LOG_FILE%" 2>&1

if not exist "%NODEJS_FILE%" (
    echo ❌ Node.js下载失败，请检查网络连接
    echo Node.js下载失败 >> "%LOG_FILE%"
    pause
    exit /b 1
)

echo 正在安装Node.js...
msiexec /i "%NODEJS_FILE%" /quiet /norestart >> "%LOG_FILE%" 2>&1

:: 等待安装完成
timeout /t 30 /nobreak >nul

:: 刷新环境变量
call refreshenv >nul 2>&1

:: 验证安装
node --version >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ Node.js安装失败，请手动安装后重试
    echo Node.js安装失败 >> "%LOG_FILE%"
    pause
    exit /b 1
)

:nodejs_done
echo ✅ Node.js准备就绪
echo.

echo [3/6] 配置MySQL数据库...
echo [3/6] 配置MySQL数据库... >> "%LOG_FILE%"

:: 检查MySQL是否已安装
mysql --version >nul 2>&1
if %errorLevel% equ 0 (
    echo ✅ 检测到MySQL，跳过安装
    goto :mysql_done
)

:: 下载MySQL便携版
echo 正在下载MySQL 8.0便携版...
set MYSQL_URL=https://dev.mysql.com/get/Downloads/MySQL-8.0/mysql-8.0.35-winx64.zip
set MYSQL_FILE=temp\mysql-portable.zip

powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri '%MYSQL_URL%' -OutFile '%MYSQL_FILE%'}" >> "%LOG_FILE%" 2>&1

if not exist "%MYSQL_FILE%" (
    echo ❌ MySQL下载失败，请检查网络连接
    echo MySQL下载失败 >> "%LOG_FILE%"
    pause
    exit /b 1
)

echo 正在解压MySQL...
powershell -Command "Expand-Archive -Path '%MYSQL_FILE%' -DestinationPath 'temp\' -Force" >> "%LOG_FILE%" 2>&1

:: 移动MySQL到目标目录
for /d %%i in (temp\mysql-*) do (
    move "%%i" "mysql" >nul 2>&1
    goto :mysql_extracted
)
:mysql_extracted

:: 创建MySQL配置文件
echo [mysqld] > mysql\my.ini
echo basedir=%CD%\mysql >> mysql\my.ini
echo datadir=%CD%\mysql\data >> mysql\my.ini
echo port=3306 >> mysql\my.ini
echo bind-address=127.0.0.1 >> mysql\my.ini
echo default-authentication-plugin=mysql_native_password >> mysql\my.ini
echo sql_mode=STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO >> mysql\my.ini

:: 初始化MySQL
echo 正在初始化MySQL数据库...
mysql\bin\mysqld --initialize-insecure --console >> "%LOG_FILE%" 2>&1

:mysql_done
echo ✅ MySQL配置完成
echo.

echo [4/6] 安装项目依赖...
echo [4/6] 安装项目依赖... >> "%LOG_FILE%"

:: 安装后端依赖
echo 正在安装后端依赖...
cd backend
call npm install >> "..\%LOG_FILE%" 2>&1
if %errorLevel% neq 0 (
    echo ❌ 后端依赖安装失败
    echo 后端依赖安装失败 >> "..\%LOG_FILE%"
    cd ..
    pause
    exit /b 1
)
cd ..

:: 安装前端依赖
echo 正在安装前端依赖...
cd frontend
call npm install >> "..\%LOG_FILE%" 2>&1
if %errorLevel% neq 0 (
    echo ❌ 前端依赖安装失败
    echo 前端依赖安装失败 >> "..\%LOG_FILE%"
    cd ..
    pause
    exit /b 1
)
cd ..

echo ✅ 项目依赖安装完成
echo.

echo [5/6] 初始化数据库...
echo [5/6] 初始化数据库... >> "%LOG_FILE%"

:: 启动MySQL服务
echo 正在启动MySQL服务...
start /b mysql\bin\mysqld --defaults-file=mysql\my.ini >> "%LOG_FILE%" 2>&1

:: 等待MySQL启动
timeout /t 10 /nobreak >nul

:: 创建数据库和用户
echo 正在创建数据库...
mysql\bin\mysql -u root --execute="CREATE DATABASE IF NOT EXISTS family_oa_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;" >> "%LOG_FILE%" 2>&1
mysql\bin\mysql -u root --execute="CREATE USER IF NOT EXISTS 'family_oa'@'localhost' IDENTIFIED BY 'FamilyOA@2024';" >> "%LOG_FILE%" 2>&1
mysql\bin\mysql -u root --execute="GRANT ALL PRIVILEGES ON family_oa_platform.* TO 'family_oa'@'localhost';" >> "%LOG_FILE%" 2>&1
mysql\bin\mysql -u root --execute="FLUSH PRIVILEGES;" >> "%LOG_FILE%" 2>&1

:: 导入数据库结构
if exist "database\schema.sql" (
    echo 正在导入数据库结构...
    mysql\bin\mysql -u family_oa -pFamilyOA@2024 family_oa_platform < database\schema.sql >> "%LOG_FILE%" 2>&1
)

echo ✅ 数据库初始化完成
echo.

echo [6/6] 生成配置文件...
echo [6/6] 生成配置文件... >> "%LOG_FILE%"

:: 生成JWT密钥
set JWT_SECRET=FamilyOA_%RANDOM%%RANDOM%%RANDOM%_Secret_Key_2024

:: 创建后端配置文件
(
echo # FamilyOAPlatform 自动生成配置文件
echo # 生成时间: %date% %time%
echo.
echo # 数据库配置
echo DB_HOST=localhost
echo DB_PORT=3306
echo DB_NAME=family_oa_platform
echo DB_USER=family_oa
echo DB_PASSWORD=FamilyOA@2024
echo.
echo # 服务器配置
echo PORT=3000
echo NODE_ENV=development
echo FRONTEND_URL=http://localhost:5173
echo.
echo # JWT 配置
echo JWT_SECRET=%JWT_SECRET%
echo JWT_EXPIRES_IN=7d
echo.
echo # Google AI 配置 ^(需要手动配置^)
echo GOOGLE_AI_API_KEY=your_google_ai_api_key_here
echo GOOGLE_AI_MODEL=gemini-2.0-flash-exp
echo.
echo # CORS 配置
echo ALLOWED_ORIGINS=http://localhost:5173,http://127.0.0.1:5173
) > backend\.env

:: 创建前端配置文件
(
echo # 前端开发环境配置
echo VITE_API_BASE_URL=http://localhost:3000/api
echo VITE_APP_TITLE=家庭办公平台
echo VITE_APP_VERSION=1.0.0
) > frontend\.env.development

echo ✅ 配置文件生成完成
echo.

:: 清理临时文件
if exist "temp" rmdir /s /q temp

echo 安装完成时间: %date% %time% >> "%LOG_FILE%"

echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🎉 安装完成！                              ║
echo ║                                                              ║
echo ║  下一步操作：                                                  ║
echo ║  1. 运行 config-wizard.bat 配置Google AI API密钥               ║
echo ║  2. 运行 start.bat 启动服务                                    ║
echo ║  3. 浏览器访问 http://localhost:5173                          ║
echo ║                                                              ║
echo ║  如遇问题，请查看日志文件：                                      ║
echo ║  %LOG_FILE%
echo ╚══════════════════════════════════════════════════════════════╝
echo.

pause
