<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>直接登录 - 家庭办公平台</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 400px;
            margin: 50px auto;
            padding: 20px;
        }
        .form-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            width: 100%;
            padding: 10px;
            background: #409EFF;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #337ecc;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .message {
            margin: 15px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #f0f9ff;
            color: #059669;
            border: 1px solid #10b981;
        }
        .error {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #ef4444;
        }
    </style>
</head>
<body>
    <h1>家庭办公平台 - 直接登录</h1>
    
    <form id="loginForm">
        <div class="form-group">
            <label for="username">用户名:</label>
            <input type="text" id="username" value="linfanhao" required>
        </div>
        
        <div class="form-group">
            <label for="password">密码:</label>
            <input type="password" id="password" value="linfanhao" required>
        </div>
        
        <button type="submit" id="loginBtn">登录</button>
    </form>
    
    <div id="message"></div>
    
    <div style="margin-top: 20px; color: #666;">
        <p>测试账户: linfanhao / linfanhao</p>
        <p>这是一个绕过Vue的直接登录页面</p>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('loginBtn');
            const messageDiv = document.getElementById('message');
            
            loginBtn.disabled = true;
            loginBtn.textContent = '登录中...';
            messageDiv.innerHTML = '';
            
            try {
                const response = await fetch('http://localhost:3002/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    // 保存token
                    localStorage.setItem('auth_token', data.token);
                    
                    messageDiv.innerHTML = '<div class="message success">登录成功！正在跳转到Vue应用...</div>';
                    
                    // 跳转到Vue应用的仪表盘
                    setTimeout(() => {
                        window.location.href = '/dashboard';
                    }, 2000);
                } else {
                    messageDiv.innerHTML = `<div class="message error">登录失败: ${data.error}</div>`;
                }
            } catch (error) {
                console.error('Login error:', error);
                messageDiv.innerHTML = `<div class="message error">登录失败: ${error.message}</div>`;
            } finally {
                loginBtn.disabled = false;
                loginBtn.textContent = '登录';
            }
        });
        
        // 测试按钮点击
        console.log('直接登录页面已加载');
    </script>
</body>
</html>
