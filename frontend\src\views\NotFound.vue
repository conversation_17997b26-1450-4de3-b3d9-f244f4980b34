<template>
  <div class="not-found-page">
    <div class="not-found-container">
      <div class="not-found-content">
        <div class="error-icon">
          <el-icon size="120"><Warning /></el-icon>
        </div>
        <h1>404</h1>
        <h2>页面未找到</h2>
        <p>抱歉，您访问的页面不存在或已被移除。</p>
        <div class="action-buttons">
          <el-button type="primary" @click="goHome">
            返回首页
          </el-button>
          <el-button @click="goBack">
            返回上页
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { Warning } from '@element-plus/icons-vue'

const router = useRouter()

const goHome = () => {
  router.push('/dashboard')
}

const goBack = () => {
  router.go(-1)
}
</script>

<style scoped>
.not-found-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
}

.not-found-container {
  text-align: center;
  padding: 40px;
}

.error-icon {
  color: #f56565;
  margin-bottom: 24px;
}

.not-found-content h1 {
  font-size: 72px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 16px;
}

.not-found-content h2 {
  font-size: 32px;
  font-weight: 600;
  color: #4a5568;
  margin-bottom: 16px;
}

.not-found-content p {
  font-size: 18px;
  color: #718096;
  margin-bottom: 32px;
}

.action-buttons {
  display: flex;
  gap: 16px;
  justify-content: center;
}

@media (max-width: 768px) {
  .not-found-content h1 {
    font-size: 48px;
  }
  
  .not-found-content h2 {
    font-size: 24px;
  }
  
  .action-buttons {
    flex-direction: column;
    align-items: center;
  }
}
</style>
