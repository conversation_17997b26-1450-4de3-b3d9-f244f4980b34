import { defineStore } from 'pinia'
import api, { aiApi } from '../utils/api'

export const useProposalStore = defineStore('proposal', {
  state: () => ({
    proposals: [],
    currentProposal: null,
    loading: false,
    pagination: {
      page: 1,
      limit: 10,
      total: 0
    }
  }),

  getters: {
    getProposalsByStatus: (state) => (status) => {
      return state.proposals.filter(proposal => proposal.status === status)
    },
    
    getProposalById: (state) => (id) => {
      return state.proposals.find(proposal => proposal.id === parseInt(id))
    }
  },

  actions: {
    async fetchProposals(params = {}) {
      this.loading = true
      try {
        const response = await api.get('/proposals', { params })
        this.proposals = response.data.proposals || response.data
        this.pagination = {
          page: response.data.page || 1,
          limit: response.data.limit || 10,
          total: response.data.total || 0
        }
        return { success: true }
      } catch (error) {
        const message = error.response?.data?.error || '获取提案列表失败'
        return { success: false, message }
      } finally {
        this.loading = false
      }
    },    async fetchProposal(id) {
      this.loading = true
      try {
        const response = await api.get(`/proposals/${id}`)
        this.currentProposal = response.data
        return { success: true, data: response.data }
      } catch (error) {
        const message = error.response?.data?.error || '获取提案详情失败'
        return { success: false, message }
      } finally {
        this.loading = false
      }
    },

    async createProposal(proposalData) {
      this.loading = true
      try {
        const response = await api.post('/proposals', proposalData)
        await this.fetchProposals() // Refresh proposals list
        return { success: true, message: response.data.message, proposalId: response.data.proposalId }
      } catch (error) {
        const message = error.response?.data?.error || '创建提案失败'
        return { success: false, message }
      } finally {
        this.loading = false
      }
    },

    async updateProposal(id, proposalData) {
      this.loading = true
      try {
        const response = await api.put(`/proposals/${id}`, proposalData)
        await this.fetchProposals() // Refresh proposals list
        return { success: true, message: response.data.message }
      } catch (error) {
        const message = error.response?.data?.error || '更新提案失败'
        return { success: false, message }
      } finally {
        this.loading = false
      }
    },

    async deleteProposal(id) {
      try {
        const response = await api.delete(`/proposals/${id}`)
        await this.fetchProposals() // Refresh proposals list
        return { success: true, message: response.data.message }
      } catch (error) {
        const message = error.response?.data?.error || '删除提案失败'
        return { success: false, message }
      }
    },

    async addComment(proposalId, commentData) {
      try {
        const response = await api.post(`/proposals/${proposalId}/comments`, commentData)
        await this.fetchProposal(proposalId) // Refresh proposal details
        return { success: true, message: response.data.message }
      } catch (error) {
        const message = error.response?.data?.error || '添加评论失败'
        return { success: false, message }
      }
    },

    async vote(proposalId, voteData) {
      try {
        const response = await api.post(`/proposals/${proposalId}/votes`, voteData)
        await this.fetchProposal(proposalId) // Refresh proposal details
        return { success: true, message: response.data.message }
      } catch (error) {
        const message = error.response?.data?.error || '投票失败'
        return { success: false, message }
      }
    },

    async expandContent(text, type = 'description') {
      try {
        const response = await aiApi.post('/llm/expand', { text, type })
        return { success: true, data: response.data }
      } catch (error) {
        const message = error.response?.data?.error || '内容扩展失败'
        return { success: false, message }
      }
    },

    async requestAIAdjudication(proposalId) {
      try {
        const response = await aiApi.post(`/llm/adjudicate/${proposalId}`)
        await this.fetchProposal(proposalId) // Refresh proposal details
        return { success: true, message: response.data.message, data: response.data }
      } catch (error) {
        const message = error.response?.data?.error || 'AI仲裁失败'
        return { success: false, message }
      }
    },

    async updateExpandedContent(proposalId, contentData) {
      try {
        const response = await api.post(`/llm/update-expanded/${proposalId}`, contentData)
        await this.fetchProposal(proposalId) // Refresh proposal details
        return { success: true, message: response.data.message }
      } catch (error) {
        const message = error.response?.data?.error || '更新扩展内容失败'
        return { success: false, message }
      }
    },

    async updateExpandedComment(commentId, expandedContent) {
      try {
        const response = await api.post(`/llm/update-expanded-comment/${commentId}`, {
          expandedContent
        })
        return { success: true, message: response.data.message }
      } catch (error) {
        const message = error.response?.data?.error || '更新扩展评论失败'
        return { success: false, message }
      }
    }
  }
})
