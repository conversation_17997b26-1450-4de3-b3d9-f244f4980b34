@echo off
chcp 65001 >nul
title FamilyOAPlatform 完全重置

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                ⚠️  FamilyOAPlatform 完全重置                   ║
echo ║                                                              ║
echo ║  警告：此操作将删除以下内容：                                     ║
echo ║  • 所有数据库数据                                              ║
echo ║  • 配置文件                                                   ║
echo ║  • 日志文件                                                   ║
echo ║  • 已安装的依赖包                                              ║
echo ║  • MySQL便携版数据                                            ║
echo ║                                                              ║
echo ║  此操作不可逆，请谨慎操作！                                      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 您确定要完全重置FamilyOAPlatform吗？
echo 这将删除所有数据和配置！
echo.
echo 输入 "YES" 确认重置，或按任意键取消：
set /p confirm="请输入: "

if not "%confirm%"=="YES" (
    echo.
    echo 重置操作已取消
    pause
    exit /b 0
)

echo.
echo 开始重置操作...
echo.

:: 创建重置日志
set LOG_FILE=logs\reset-%date:~0,4%%date:~5,2%%date:~8,2%-%time:~0,2%%time:~3,2%%time:~6,2%.log
if not exist "logs" mkdir logs
echo FamilyOAPlatform 完全重置日志 > "%LOG_FILE%"
echo 重置时间: %date% %time% >> "%LOG_FILE%"
echo ═══════════════════════════════════════════════════════════════ >> "%LOG_FILE%"

echo [1/6] 停止所有服务...
echo [1/6] 停止所有服务... >> "%LOG_FILE%"

:: 停止所有相关进程
taskkill /F /IM node.exe >nul 2>&1
taskkill /F /IM mysqld.exe >nul 2>&1

:: 停止特定窗口的进程
for /f "tokens=2" %%a in ('tasklist /FI "WINDOWTITLE eq FamilyOA-*" /FO CSV /NH 2^>nul ^| findstr /V "INFO:"') do (
    if not "%%a"=="" (
        taskkill /PID %%a /F >nul 2>&1
        echo 停止进程 %%a >> "%LOG_FILE%"
    )
)

echo ✅ 服务已停止

echo [2/6] 删除数据库数据...
echo [2/6] 删除数据库数据... >> "%LOG_FILE%"

if exist "mysql\data" (
    echo 正在删除MySQL数据目录...
    rmdir /s /q "mysql\data" >nul 2>&1
    echo MySQL数据目录已删除 >> "%LOG_FILE%"
)

if exist "mysql" (
    echo 正在删除MySQL便携版...
    rmdir /s /q "mysql" >nul 2>&1
    echo MySQL便携版已删除 >> "%LOG_FILE%"
)

echo ✅ 数据库数据已删除

echo [3/6] 删除配置文件...
echo [3/6] 删除配置文件... >> "%LOG_FILE%"

if exist "backend\.env" (
    del "backend\.env" >nul 2>&1
    echo 后端配置文件已删除 >> "%LOG_FILE%"
)

if exist "backend\.env.backup" (
    del "backend\.env.backup" >nul 2>&1
    echo 后端配置备份已删除 >> "%LOG_FILE%"
)

if exist "frontend\.env.development" (
    del "frontend\.env.development" >nul 2>&1
    echo 前端配置文件已删除 >> "%LOG_FILE%"
)

if exist "frontend\.env.production" (
    del "frontend\.env.production" >nul 2>&1
    echo 前端生产配置已删除 >> "%LOG_FILE%"
)

echo ✅ 配置文件已删除

echo [4/6] 删除依赖包...
echo [4/6] 删除依赖包... >> "%LOG_FILE%"

if exist "backend\node_modules" (
    echo 正在删除后端依赖包...
    rmdir /s /q "backend\node_modules" >nul 2>&1
    echo 后端依赖包已删除 >> "%LOG_FILE%"
)

if exist "frontend\node_modules" (
    echo 正在删除前端依赖包...
    rmdir /s /q "frontend\node_modules" >nul 2>&1
    echo 前端依赖包已删除 >> "%LOG_FILE%"
)

if exist "node_modules" (
    echo 正在删除根目录依赖包...
    rmdir /s /q "node_modules" >nul 2>&1
    echo 根目录依赖包已删除 >> "%LOG_FILE%"
)

echo ✅ 依赖包已删除

echo [5/6] 删除构建文件...
echo [5/6] 删除构建文件... >> "%LOG_FILE%"

if exist "frontend\dist" (
    rmdir /s /q "frontend\dist" >nul 2>&1
    echo 前端构建文件已删除 >> "%LOG_FILE%"
)

if exist "backend\dist" (
    rmdir /s /q "backend\dist" >nul 2>&1
    echo 后端构建文件已删除 >> "%LOG_FILE%"
)

echo ✅ 构建文件已删除

echo [6/6] 清理临时文件...
echo [6/6] 清理临时文件... >> "%LOG_FILE%"

if exist "temp" (
    rmdir /s /q "temp" >nul 2>&1
    echo 临时文件已删除 >> "%LOG_FILE%"
)

:: 清理npm缓存
npm cache clean --force >nul 2>&1
echo npm缓存已清理 >> "%LOG_FILE%"

echo ✅ 临时文件已清理

echo.
echo 重置完成时间: %date% %time% >> "%LOG_FILE%"

echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🎉 重置完成！                              ║
echo ║                                                              ║
echo ║  FamilyOAPlatform已完全重置到初始状态                           ║
echo ║                                                              ║
echo ║  下一步操作：                                                  ║
echo ║  1. 运行 check-system.bat 检查系统环境                         ║
echo ║  2. 运行 install.bat 重新安装                                  ║
echo ║  3. 运行 config-wizard.bat 重新配置                            ║
echo ║  4. 运行 start.bat 启动服务                                    ║
echo ║                                                              ║
echo ║  重置日志: %LOG_FILE%
echo ╚══════════════════════════════════════════════════════════════╝
echo.

pause
