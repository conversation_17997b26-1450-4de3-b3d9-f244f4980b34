<template>
  <div class="profile-page">
    <!-- Page Header -->
    <div class="page-header">
      <div class="header-content">
        <h1>个人资料</h1>
        <p>管理您的个人信息</p>
      </div>
    </div>

    <el-row :gutter="24">
      <!-- Profile Form -->
      <el-col :xs="24" :md="16">
        <el-card class="profile-card">
          <template #header>
            <h3>基本信息</h3>
          </template>
          
          <el-form
            ref="profileFormRef"
            :model="profileForm"
            :rules="profileRules"
            label-width="100px"
            class="profile-form"
          >
            <el-form-item label="用户名">
              <el-input v-model="authStore.user.username" disabled />
            </el-form-item>
            
            <el-form-item label="真实姓名" prop="realName">
              <el-input v-model="profileForm.realName" placeholder="请输入真实姓名" />
            </el-form-item>
            

            
            <el-form-item label="注册时间">
              <span class="registration-date">
                {{ formatDate(authStore.user.created_at) }}
              </span>
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="updateProfile" :loading="updating">
                保存修改
              </el-button>
              <el-button @click="resetForm">
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
      
      <!-- Statistics -->
      <el-col :xs="24" :md="8">
        <el-card class="stats-card">
          <template #header>
            <h3>我的统计</h3>
          </template>
          
          <div v-if="loadingStats" class="loading-container">
            <el-skeleton :rows="4" animated />
          </div>
          
          <div v-else class="stats-list">
            <div class="stat-item">
              <div class="stat-icon proposals">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stat-info">
                <h4>{{ userStats.proposals?.total_proposals || 0 }}</h4>
                <p>创建的提案</p>
              </div>
            </div>
            
            <div class="stat-item">
              <div class="stat-icon comments">
                <el-icon><ChatDotRound /></el-icon>
              </div>
              <div class="stat-info">
                <h4>{{ userStats.comments?.total_comments || 0 }}</h4>
                <p>发表的评论</p>
              </div>
            </div>
            
            <div class="stat-item">
              <div class="stat-icon votes">
                <el-icon><Select /></el-icon>
              </div>
              <div class="stat-info">
                <h4>{{ userStats.votes?.total_votes || 0 }}</h4>
                <p>参与的投票</p>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Document, ChatDotRound, Select } from '@element-plus/icons-vue'
import { useAuthStore } from '../stores/auth'
import axios from 'axios'

const authStore = useAuthStore()

const profileFormRef = ref()
const updating = ref(false)
const loadingStats = ref(false)
const userStats = ref({})

const profileForm = reactive({
  realName: ''
})

const profileRules = {
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '姓名长度在2到20个字符', trigger: 'blur' }
  ]
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const initForm = () => {
  if (authStore.user) {
    profileForm.realName = authStore.user.real_name || ''
  }
}

const updateProfile = async () => {
  if (!profileFormRef.value) return

  const valid = await profileFormRef.value.validate().catch(() => false)
  if (!valid) return

  updating.value = true
  try {
    const result = await authStore.updateProfile(profileForm)
    
    if (result.success) {
      ElMessage.success(result.message)
    } else {
      ElMessage.error(result.message)
    }
  } catch (error) {
    ElMessage.error('更新失败')
  } finally {
    updating.value = false
  }
}

const resetForm = () => {
  initForm()
  ElMessage.info('表单已重置')
}

const loadUserStats = async () => {
  loadingStats.value = true
  try {
    const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3002/api'
    const response = await axios.get(`${API_BASE_URL}/users/stats`)
    userStats.value = response.data
  } catch (error) {
    console.error('加载统计信息失败:', error)
  } finally {
    loadingStats.value = false
  }
}

onMounted(() => {
  initForm()
  loadUserStats()
})
</script>

<style scoped>
.profile-page {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;
}

.header-content h1 {
  font-size: 28px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 8px;
}

.header-content p {
  color: #718096;
  font-size: 16px;
}

.profile-card,
.stats-card {
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  margin-bottom: 24px;
}

.profile-form {
  max-width: 500px;
}

.registration-date {
  color: #718096;
  font-size: 14px;
}

.loading-container {
  padding: 20px;
}

.stats-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
}

.stat-icon.proposals {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.comments {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
}

.stat-icon.votes {
  background: linear-gradient(135deg, #f6ad55 0%, #ed8936 100%);
}

.stat-info h4 {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 4px;
  color: #2d3748;
}

.stat-info p {
  color: #718096;
  font-size: 14px;
}

@media (max-width: 768px) {
  .header-content h1 {
    font-size: 24px;
  }
  
  .profile-form {
    max-width: 100%;
  }
  
  .stat-item {
    padding: 12px;
  }
  
  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }
  
  .stat-info h4 {
    font-size: 20px;
  }
}
</style>
