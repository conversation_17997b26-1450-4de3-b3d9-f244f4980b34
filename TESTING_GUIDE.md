# 🧪 FamilyOAPlatform 测试流程文档

## 📋 目录
- [环境准备](#环境准备)
- [启动项目](#启动项目)
- [功能测试](#功能测试)
- [性能测试](#性能测试)
- [安全测试](#安全测试)
- [错误处理测试](#错误处理测试)
- [移动端测试](#移动端测试)
- [自动化测试](#自动化测试)

## 🔧 环境准备

### 系统要求
- **Node.js**: >= 16.0.0
- **MySQL**: >= 8.0
- **浏览器**: Chrome 90+, Firefox 88+, Safari 14+
- **操作系统**: Windows 10+, macOS 10.15+, Ubuntu 18.04+

### 环境配置检查清单
- [ ] Node.js 版本正确 (`node --version`)
- [ ] MySQL 服务运行中
- [ ] 数据库 `family_oa_platform` 已创建
- [ ] Google AI API 密钥已配置
- [ ] 网络连接正常

## 🚀 启动项目

### 1. 数据库初始化
```sql
-- 创建数据库
CREATE DATABASE family_oa_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE family_oa_platform;

-- 导入schema（可选，应用会自动创建表）
SOURCE database/schema.sql;
```

### 2. 后端启动
```bash
cd backend
npm install
cp .env.example .env
# 编辑 .env 文件，配置数据库和API密钥
npm run dev
```

**验证后端启动成功**:
- 访问 http://localhost:3000/health
- 应返回: `{"status":"OK","message":"家庭办公平台后端运行正常"}`

### 3. 前端启动
```bash
cd frontend
npm install
npm run dev
```

**验证前端启动成功**:
- 访问 http://localhost:5173
- 应显示登录页面

## 🧪 功能测试

### 用户认证测试

#### 1. 用户注册
**测试步骤**:
1. 访问 http://localhost:5173/register
2. 填写注册表单:
   - 用户名: `testuser`
   - 邮箱: `<EMAIL>`
   - 密码: `Test123!@#`
   - 真实姓名: `测试用户`
3. 点击"注册"按钮

**预期结果**:
- [ ] 表单验证正常工作
- [ ] 注册成功后显示成功消息
- [ ] 自动跳转到登录页面

#### 2. 用户登录
**测试步骤**:
1. 使用默认管理员账户登录:
   - 用户名: `admin`
   - 密码: `admin123`
2. 或使用刚注册的账户登录

**预期结果**:
- [ ] 登录成功后跳转到仪表盘
- [ ] 顶部显示用户信息
- [ ] 侧边栏显示正确的菜单项

### 提案管理测试

#### 1. 创建提案
**测试步骤**:
1. 点击"创建提案"菜单
2. 填写提案信息:
   - 标题: `测试提案 - 购买新电视`
   - 描述: `客厅的电视已经用了很多年，建议更换一台新的智能电视`
   - 解决方案: `预算5000元购买55寸4K智能电视`
   - 优先级: `medium`
   - 截止日期: 选择未来日期
3. 点击"创建提案"

**预期结果**:
- [ ] 表单验证正常
- [ ] 创建成功后显示成功消息
- [ ] 跳转到提案详情页面
- [ ] 提案状态为"草稿"

#### 2. AI内容扩展测试
**测试步骤**:
1. 在创建提案页面
2. 点击"AI扩展描述"按钮
3. 等待AI处理

**预期结果**:
- [ ] 显示加载状态
- [ ] AI扩展内容合理且相关
- [ ] 扩展内容自动填入对应字段

#### 3. 提案状态管理
**测试步骤**:
1. 进入提案详情页面
2. 测试状态变更: 草稿 → 待审核 → 通过/拒绝 → 完成
3. 验证每个状态变更的权限控制

**预期结果**:
- [ ] 状态变更按钮正确显示
- [ ] 权限控制正常工作
- [ ] 状态变更后界面及时更新

### 协作功能测试

#### 1. 评论功能
**测试步骤**:
1. 在提案详情页面添加评论
2. 测试不同类型的评论: 支持、反对、中立、疑问
3. 测试评论的编辑和删除

**预期结果**:
- [ ] 评论成功添加
- [ ] 评论类型正确显示
- [ ] 评论时间正确
- [ ] 权限控制正常

#### 2. 投票功能
**测试步骤**:
1. 对提案进行投票: 赞成、反对、弃权
2. 修改投票选择
3. 查看投票统计

**预期结果**:
- [ ] 投票成功记录
- [ ] 投票统计正确更新
- [ ] 一个用户只能投一票

### AI仲裁测试

**测试步骤**:
1. 在有评论和投票的提案上
2. 点击"AI仲裁"按钮
3. 等待AI分析结果

**预期结果**:
- [ ] AI分析合理客观
- [ ] 评分在0-10范围内
- [ ] 提供具体的改进建议

## ⚡ 性能测试

### 1. 页面加载性能
**测试工具**: Chrome DevTools Performance
**测试步骤**:
1. 打开Chrome DevTools
2. 切换到Performance标签
3. 刷新页面并记录性能数据

**性能指标**:
- [ ] 首次内容绘制(FCP) < 1.5s
- [ ] 最大内容绘制(LCP) < 2.5s
- [ ] 首次输入延迟(FID) < 100ms
- [ ] 累积布局偏移(CLS) < 0.1

### 2. API响应性能
**测试工具**: Postman 或 curl
**测试接口**:
```bash
# 健康检查
curl http://localhost:3000/health

# 登录接口
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'

# 获取提案列表
curl http://localhost:3000/api/proposals \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**性能要求**:
- [ ] API响应时间 < 500ms
- [ ] 数据库查询时间 < 200ms
- [ ] AI接口响应时间 < 5s

### 3. 并发测试
**测试工具**: Apache Bench (ab)
```bash
# 测试登录接口并发性能
ab -n 100 -c 10 -p login.json -T application/json http://localhost:3000/api/auth/login
```

**并发要求**:
- [ ] 支持10个并发用户
- [ ] 错误率 < 1%
- [ ] 平均响应时间 < 1s

## 🔒 安全测试

### 1. 身份验证测试
**测试场景**:
- [ ] 未登录访问受保护页面被重定向
- [ ] Token过期后自动退出登录
- [ ] 无效Token被拒绝
- [ ] 密码强度验证正常

### 2. 输入验证测试
**测试数据**:
```javascript
// SQL注入测试
username: "admin'; DROP TABLE users; --"

// XSS测试
title: "<script>alert('XSS')</script>"

// 长度测试
description: "A".repeat(10000)
```

**预期结果**:
- [ ] 恶意输入被正确过滤
- [ ] 错误消息不泄露敏感信息
- [ ] 输入长度限制生效

### 3. 权限控制测试
**测试场景**:
- [ ] 普通用户无法访问管理功能
- [ ] 用户只能编辑自己的提案
- [ ] API接口权限验证正常

## 🐛 错误处理测试

### 1. 网络错误测试
**测试步骤**:
1. 断开网络连接
2. 尝试执行各种操作
3. 恢复网络连接

**预期结果**:
- [ ] 显示友好的错误消息
- [ ] 提供重试机制
- [ ] 网络恢复后自动重连

### 2. 服务器错误测试
**测试步骤**:
1. 停止后端服务
2. 在前端执行操作
3. 重启后端服务

**预期结果**:
- [ ] 错误边界正常工作
- [ ] 不会导致页面崩溃
- [ ] 提供错误报告功能

### 3. 数据库错误测试
**测试步骤**:
1. 停止MySQL服务
2. 尝试数据库操作
3. 重启MySQL服务

**预期结果**:
- [ ] 优雅降级处理
- [ ] 连接池自动重连
- [ ] 错误日志记录完整

## 📱 移动端测试

### 响应式设计测试
**测试设备**:
- [ ] iPhone 12 (390x844)
- [ ] iPad (768x1024)
- [ ] Android Phone (360x640)
- [ ] Desktop (1920x1080)

**测试要点**:
- [ ] 布局在不同屏幕尺寸下正常
- [ ] 触摸操作响应正常
- [ ] 字体大小适中
- [ ] 按钮大小适合触摸

### 移动端性能测试
**测试工具**: Chrome DevTools Mobile Simulation
**性能要求**:
- [ ] 3G网络下加载时间 < 5s
- [ ] 滚动流畅度 > 60fps
- [ ] 内存使用 < 50MB

## 🤖 自动化测试

### 单元测试
```bash
# 后端单元测试
cd backend
npm test

# 前端单元测试
cd frontend
npm run test:unit
```

### 集成测试
```bash
# API集成测试
cd backend
npm run test:integration
```

### E2E测试
```bash
# 端到端测试
cd frontend
npm run test:e2e
```

## 📊 测试报告模板

### 测试执行记录
| 测试项目 | 状态 | 执行时间 | 备注 |
|---------|------|----------|------|
| 用户注册 | ✅ | 2023-12-XX | 正常 |
| 用户登录 | ✅ | 2023-12-XX | 正常 |
| 创建提案 | ❌ | 2023-12-XX | AI接口超时 |
| ... | ... | ... | ... |

### 问题跟踪
| 问题ID | 严重程度 | 问题描述 | 状态 | 修复时间 |
|--------|----------|----------|------|----------|
| BUG-001 | 高 | AI接口响应超时 | 已修复 | 2023-12-XX |
| BUG-002 | 中 | 移动端布局问题 | 进行中 | - |

### 性能指标
| 指标 | 目标值 | 实际值 | 状态 |
|------|--------|--------|------|
| 页面加载时间 | < 2s | 1.5s | ✅ |
| API响应时间 | < 500ms | 300ms | ✅ |
| 并发用户数 | 10 | 15 | ✅ |

## 🔧 故障排除

### 常见问题及解决方案

1. **后端启动失败**
   - 检查MySQL服务是否运行
   - 验证.env文件配置
   - 检查端口3000是否被占用

2. **前端无法连接后端**
   - 确认后端服务正常运行
   - 检查CORS配置
   - 验证API基础URL配置

3. **AI功能不工作**
   - 检查Google AI API密钥
   - 验证网络连接
   - 查看API配额限制

4. **数据库连接失败**
   - 检查数据库服务状态
   - 验证连接参数
   - 检查防火墙设置

## 📞 技术支持

如遇到测试过程中的问题，请：
1. 查看控制台错误日志
2. 检查网络请求状态
3. 参考故障排除指南
4. 联系开发团队

---

**测试完成标准**: 所有核心功能测试通过，性能指标达标，安全测试无重大漏洞。
