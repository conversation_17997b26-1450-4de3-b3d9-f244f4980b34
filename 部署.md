# 🏠 FamilyOAPlatform 家庭局域网部署指南

## 📋 目录
- [系统环境准备](#系统环境准备)
- [数据库配置](#数据库配置)
- [项目部署配置](#项目部署配置)
- [Google AI API配置](#google-ai-api配置)
- [局域网访问配置](#局域网访问配置)
- [启动和测试](#启动和测试)
- [生产环境优化](#生产环境优化)
- [常见问题排查](#常见问题排查)

## 🖥️ 系统环境准备

### 推荐服务器配置
- **操作系统**: Ubuntu 20.04 LTS / CentOS 8 / Windows Server 2019+
- **CPU**: 2核心以上
- **内存**: 4GB RAM（推荐8GB）
- **存储**: 20GB可用空间
- **网络**: 千兆局域网连接

### 必需软件安装

#### Ubuntu/Debian 系统
```bash
# 更新系统包
sudo apt update && sudo apt upgrade -y

# 安装 Node.js 18.x LTS
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装 MySQL 8.0
sudo apt install mysql-server -y

# 安装其他必需工具
sudo apt install git curl wget unzip -y

# 验证安装
node --version  # 应显示 v18.x.x
npm --version   # 应显示 9.x.x
mysql --version # 应显示 8.0.x
```

#### CentOS/RHEL 系统
```bash
# 更新系统
sudo yum update -y

# 安装 Node.js 18.x LTS
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo yum install -y nodejs

# 安装 MySQL 8.0
sudo yum install mysql-server -y

# 安装其他工具
sudo yum install git curl wget unzip -y
```

#### Windows Server 系统
1. 下载并安装 [Node.js 18.x LTS](https://nodejs.org/)
2. 下载并安装 [MySQL 8.0](https://dev.mysql.com/downloads/mysql/)
3. 安装 [Git for Windows](https://git-scm.com/download/win)

### 防火墙配置

#### Ubuntu/Debian (UFW)
```bash
# 启用防火墙
sudo ufw enable

# 开放必要端口
sudo ufw allow 22      # SSH
sudo ufw allow 3000    # 后端API
sudo ufw allow 5173    # 前端服务
sudo ufw allow 3306    # MySQL (仅局域网)

# 查看状态
sudo ufw status
```

#### CentOS/RHEL (firewalld)
```bash
# 启动防火墙服务
sudo systemctl start firewalld
sudo systemctl enable firewalld

# 开放端口
sudo firewall-cmd --permanent --add-port=3000/tcp
sudo firewall-cmd --permanent --add-port=5173/tcp
sudo firewall-cmd --permanent --add-port=3306/tcp
sudo firewall-cmd --reload
```

## 🗄️ 数据库配置

### MySQL 安装和初始化

#### 1. 启动 MySQL 服务
```bash
# Ubuntu/Debian
sudo systemctl start mysql
sudo systemctl enable mysql

# CentOS/RHEL
sudo systemctl start mysqld
sudo systemctl enable mysqld
```

#### 2. 安全配置
```bash
# 运行安全配置脚本
sudo mysql_secure_installation

# 配置选项建议：
# - 设置root密码: 123456 (或您的自定义密码)
# - 移除匿名用户: Y
# - 禁止root远程登录: N (局域网需要)
# - 移除测试数据库: Y
# - 重新加载权限表: Y
```

#### 3. 创建数据库和用户
```bash
# 登录MySQL
mysql -u root -p
# 输入密码: 123456
```

```sql
-- 创建数据库
CREATE DATABASE family_oa_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建专用用户（推荐）
CREATE USER 'family_oa'@'localhost' IDENTIFIED BY 'FamilyOA@2024';
CREATE USER 'family_oa'@'%' IDENTIFIED BY 'FamilyOA@2024';

-- 授予权限
GRANT ALL PRIVILEGES ON family_oa_platform.* TO 'family_oa'@'localhost';
GRANT ALL PRIVILEGES ON family_oa_platform.* TO 'family_oa'@'%';

-- 刷新权限
FLUSH PRIVILEGES;

-- 退出
EXIT;
```

#### 4. 配置远程访问
```bash
# 编辑MySQL配置文件
sudo nano /etc/mysql/mysql.conf.d/mysqld.cnf

# 找到并修改以下行：
# bind-address = 127.0.0.1
# 改为：
# bind-address = 0.0.0.0

# 重启MySQL服务
sudo systemctl restart mysql
```

## 📦 项目部署配置

### 1. 获取项目源码
```bash
# 创建项目目录
sudo mkdir -p /opt/family-oa-platform
sudo chown $USER:$USER /opt/family-oa-platform
cd /opt/family-oa-platform

# 如果有Git仓库
git clone <your-repository-url> .

# 或者上传并解压源码包
# scp family-oa-platform.zip user@server-ip:/opt/family-oa-platform/
# unzip family-oa-platform.zip
```

### 2. 后端配置

#### 安装依赖
```bash
cd /opt/family-oa-platform/backend
npm install --production
```

#### 配置环境变量
```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
nano .env
```

**完整的 .env 配置文件示例：**
```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=family_oa_platform
DB_USER=family_oa
DB_PASSWORD=FamilyOA@2024

# 服务器配置
PORT=3000
NODE_ENV=production
FRONTEND_URL=http://*************:5173

# JWT 配置
JWT_SECRET=FamilyOA_Super_Secret_Key_2024_!@#$%^&*
JWT_EXPIRES_IN=7d

# Google AI 配置
GOOGLE_AI_API_KEY=your_google_ai_api_key_here
GOOGLE_AI_MODEL=gemini-2.0-flash-exp

# CORS 配置 (局域网IP范围)
ALLOWED_ORIGINS=http://*************:5173,http://localhost:5173,http://127.0.0.1:5173

# 数据库连接池配置
DB_CONNECTION_LIMIT=20
DB_QUEUE_LIMIT=0
DB_ACQUIRE_TIMEOUT=60000
DB_TIMEOUT=60000

# 安全配置
DB_SSL=false
```

**重要配置说明：**
- `DB_PASSWORD`: 使用上面创建的数据库用户密码
- `FRONTEND_URL`: 替换为服务器的实际IP地址
- `JWT_SECRET`: 使用强密码，至少32位字符
- `ALLOWED_ORIGINS`: 添加所有可能访问的IP地址

#### 导入数据库结构
```bash
# 在项目根目录执行
cd /opt/family-oa-platform
mysql -u family_oa -p family_oa_platform < database/schema.sql
# 输入密码: FamilyOA@2024
```

### 3. 前端配置

#### 安装依赖
```bash
cd /opt/family-oa-platform/frontend
npm install
```

#### 修改配置文件

**编辑 vite.config.js：**
```bash
nano vite.config.js
```

```javascript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  server: {
    port: 5173,
    host: '0.0.0.0',  // 允许外部访问
    proxy: {
      '/api': {
        target: 'http://localhost:3000',  // 后端API地址
        changeOrigin: true,
        secure: false
      }
    }
  },
  build: {
    outDir: 'dist',
    sourcemap: false,
    chunkSizeWarningLimit: 1000,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          elementPlus: ['element-plus', '@element-plus/icons-vue'],
          utils: ['axios']
        }
      }
    }
  },
  define: {
    __VUE_OPTIONS_API__: false,
    __VUE_PROD_DEVTOOLS__: false
  }
})
```

**创建生产环境配置：**
```bash
# 创建 .env.production 文件
nano .env.production
```

```env
# 生产环境配置
VITE_API_BASE_URL=http://*************:3000/api
VITE_APP_TITLE=家庭办公平台
VITE_APP_VERSION=1.0.0
```

**注意：** 将 `*************` 替换为您服务器的实际IP地址。

## 🔑 Google AI API配置

### 1. 获取API密钥
1. 访问 [Google AI Studio](https://makersuite.google.com/app/apikey)
2. 使用Google账户登录
3. 点击 "Create API Key"
4. 选择项目或创建新项目
5. 复制生成的API密钥

### 2. 配置API密钥
```bash
# 编辑后端环境变量
nano /opt/family-oa-platform/backend/.env

# 将获取的API密钥填入：
GOOGLE_AI_API_KEY=AIzaSyC-your-actual-api-key-here
```

### 3. 测试API连接
```bash
cd /opt/family-oa-platform/backend
node -e "
const { GoogleGenerativeAI } = require('@google/generative-ai');
const genAI = new GoogleGenerativeAI('YOUR_API_KEY');
console.log('Google AI API 配置成功');
"
```

## 🌐 局域网访问配置

### 1. 确定服务器IP地址
```bash
# 查看服务器IP地址
ip addr show | grep inet
# 或者
hostname -I
```

假设服务器IP为：`*************`

### 2. 端口分配策略
- **后端API**: 3000端口
- **前端服务**: 5173端口
- **MySQL数据库**: 3306端口（仅内部访问）

### 3. 网络配置检查
```bash
# 检查端口是否被占用
sudo netstat -tlnp | grep :3000
sudo netstat -tlnp | grep :5173

# 测试端口连通性
telnet ************* 3000
telnet ************* 5173
```

### 4. 局域网设备访问方法

**从其他设备访问：**
- 前端界面：`http://*************:5173`
- 后端API：`http://*************:3000`

**移动设备访问：**
- 确保移动设备连接到同一WiFi网络
- 在浏览器中输入：`http://*************:5173`

## 🚀 启动和测试

### 1. 启动服务

#### 方式一：开发模式启动
```bash
# 启动后端服务
cd /opt/family-oa-platform/backend
npm run dev

# 新开终端，启动前端服务
cd /opt/family-oa-platform/frontend
npm run dev
```

#### 方式二：生产模式启动
```bash
# 构建前端
cd /opt/family-oa-platform/frontend
npm run build

# 启动后端
cd /opt/family-oa-platform/backend
NODE_ENV=production npm start
```

### 2. 功能测试检查清单

#### 基础连接测试
- [ ] 后端健康检查：`curl http://*************:3000/health`
- [ ] 前端页面访问：浏览器打开 `http://*************:5173`
- [ ] 数据库连接：检查后端启动日志

#### 功能测试
- [ ] 用户注册和登录
- [ ] 创建提案
- [ ] AI内容扩展功能
- [ ] 投票和评论功能
- [ ] AI仲裁功能

#### 局域网访问测试
- [ ] 从其他电脑访问
- [ ] 从手机/平板访问
- [ ] 多用户同时访问

### 3. 测试命令
```bash
# 测试后端API
curl -X GET http://*************:3000/health

# 测试用户注册
curl -X POST http://*************:3000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username":"test","password":"test123","realName":"测试用户"}'

# 测试数据库连接
mysql -h ************* -u family_oa -p family_oa_platform -e "SHOW TABLES;"
```

## 🔧 生产环境优化

### 1. 进程管理 (PM2)

#### 安装PM2
```bash
# 全局安装PM2
sudo npm install -g pm2

# 验证安装
pm2 --version
```

#### 创建PM2配置文件
```bash
# 在项目根目录创建 ecosystem.config.js
nano /opt/family-oa-platform/ecosystem.config.js
```

```javascript
module.exports = {
  apps: [
    {
      name: 'family-oa-backend',
      script: './backend/server.js',
      cwd: '/opt/family-oa-platform',
      instances: 2,
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      error_file: './logs/backend-error.log',
      out_file: './logs/backend-out.log',
      log_file: './logs/backend-combined.log',
      time: true,
      max_memory_restart: '1G',
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s'
    },
    {
      name: 'family-oa-frontend',
      script: 'npm',
      args: 'run preview',
      cwd: '/opt/family-oa-platform/frontend',
      instances: 1,
      env: {
        NODE_ENV: 'production',
        PORT: 5173
      },
      error_file: './logs/frontend-error.log',
      out_file: './logs/frontend-out.log',
      log_file: './logs/frontend-combined.log',
      time: true
    }
  ]
};
```

#### 使用PM2启动服务
```bash
# 创建日志目录
mkdir -p /opt/family-oa-platform/logs

# 构建前端
cd /opt/family-oa-platform/frontend
npm run build

# 启动所有服务
cd /opt/family-oa-platform
pm2 start ecosystem.config.js

# 查看服务状态
pm2 status

# 查看日志
pm2 logs

# 监控服务
pm2 monit
```

### 2. 开机自启动配置

#### 生成PM2启动脚本
```bash
# 保存当前PM2进程列表
pm2 save

# 生成开机启动脚本
pm2 startup

# 按照提示执行生成的命令（通常需要sudo权限）
# 例如：sudo env PATH=$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u $USER --hp $HOME
```

#### 创建系统服务（备选方案）
```bash
# 创建systemd服务文件
sudo nano /etc/systemd/system/family-oa.service
```

```ini
[Unit]
Description=Family OA Platform
After=network.target mysql.service

[Service]
Type=forking
User=your-username
WorkingDirectory=/opt/family-oa-platform
ExecStart=/usr/bin/pm2 start ecosystem.config.js
ExecReload=/usr/bin/pm2 reload ecosystem.config.js
ExecStop=/usr/bin/pm2 stop ecosystem.config.js
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

```bash
# 启用服务
sudo systemctl daemon-reload
sudo systemctl enable family-oa.service
sudo systemctl start family-oa.service

# 检查服务状态
sudo systemctl status family-oa.service
```

### 3. 日志管理

#### 配置日志轮转
```bash
# 创建logrotate配置
sudo nano /etc/logrotate.d/family-oa
```

```
/opt/family-oa-platform/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 your-username your-username
    postrotate
        pm2 reloadLogs
    endscript
}
```

#### 日志查看命令
```bash
# 查看实时日志
pm2 logs family-oa-backend --lines 100

# 查看错误日志
tail -f /opt/family-oa-platform/logs/backend-error.log

# 清空日志
pm2 flush
```

### 4. 性能优化

#### 系统优化
```bash
# 增加文件描述符限制
echo "* soft nofile 65536" | sudo tee -a /etc/security/limits.conf
echo "* hard nofile 65536" | sudo tee -a /etc/security/limits.conf

# 优化网络参数
echo "net.core.somaxconn = 65535" | sudo tee -a /etc/sysctl.conf
echo "net.ipv4.tcp_max_syn_backlog = 65535" | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

#### MySQL优化
```bash
# 编辑MySQL配置
sudo nano /etc/mysql/mysql.conf.d/mysqld.cnf
```

```ini
[mysqld]
# 基础配置
max_connections = 200
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT

# 查询缓存
query_cache_type = 1
query_cache_size = 128M

# 慢查询日志
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2
```

```bash
# 重启MySQL应用配置
sudo systemctl restart mysql
```

### 5. 安全加固

#### 防火墙规则优化
```bash
# 限制MySQL访问（仅本地）
sudo ufw delete allow 3306
sudo ufw allow from 127.0.0.1 to any port 3306

# 限制API访问（仅局域网）
sudo ufw allow from ***********/16 to any port 3000
sudo ufw allow from ***********/16 to any port 5173
```

#### SSL证书配置（可选）
```bash
# 安装nginx作为反向代理
sudo apt install nginx -y

# 生成自签名证书（局域网使用）
sudo openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout /etc/ssl/private/family-oa.key \
  -out /etc/ssl/certs/family-oa.crt
```

## 🔍 常见问题排查

### 1. 服务启动问题

#### 端口被占用
```bash
# 查看端口占用
sudo lsof -i :3000
sudo lsof -i :5173

# 杀死占用进程
sudo kill -9 <PID>
```

#### 权限问题
```bash
# 修改项目目录权限
sudo chown -R $USER:$USER /opt/family-oa-platform
chmod -R 755 /opt/family-oa-platform
```

#### Node.js版本问题
```bash
# 检查Node.js版本
node --version

# 如果版本过低，重新安装
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

### 2. 数据库连接问题

#### 连接被拒绝
```bash
# 检查MySQL服务状态
sudo systemctl status mysql

# 检查MySQL配置
sudo nano /etc/mysql/mysql.conf.d/mysqld.cnf
# 确保 bind-address = 0.0.0.0

# 重启MySQL
sudo systemctl restart mysql
```

#### 权限问题
```sql
-- 重新授权
mysql -u root -p
GRANT ALL PRIVILEGES ON family_oa_platform.* TO 'family_oa'@'%';
FLUSH PRIVILEGES;
```

### 3. 网络访问问题

#### 无法从其他设备访问
```bash
# 检查防火墙状态
sudo ufw status

# 检查服务绑定地址
netstat -tlnp | grep :3000
netstat -tlnp | grep :5173

# 确保服务绑定到 0.0.0.0 而不是 127.0.0.1
```

#### CORS跨域问题
```bash
# 检查后端CORS配置
nano /opt/family-oa-platform/backend/.env
# 确保 ALLOWED_ORIGINS 包含所有访问IP
```

### 4. AI功能问题

#### API密钥无效
```bash
# 测试API密钥
curl -H "Authorization: Bearer YOUR_API_KEY" \
  https://generativelanguage.googleapis.com/v1/models
```

#### 请求超时
```bash
# 检查网络连接
ping google.com

# 检查防火墙是否阻止外网访问
curl -I https://google.com
```

### 5. 性能问题

#### 内存不足
```bash
# 查看内存使用
free -h
pm2 monit

# 重启服务释放内存
pm2 restart all
```

#### 数据库慢查询
```bash
# 查看慢查询日志
sudo tail -f /var/log/mysql/slow.log

# 优化数据库
mysql -u root -p -e "OPTIMIZE TABLE family_oa_platform.proposals;"
```

### 6. 日志查看和调试

#### 查看详细日志
```bash
# PM2日志
pm2 logs --lines 200

# 系统日志
sudo journalctl -u family-oa.service -f

# MySQL日志
sudo tail -f /var/log/mysql/error.log
```

#### 调试模式启动
```bash
# 停止PM2服务
pm2 stop all

# 手动启动查看详细错误
cd /opt/family-oa-platform/backend
DEBUG=* npm start
```

## 📞 技术支持

如果遇到无法解决的问题，请：

1. **收集错误信息**：
   - 错误日志截图
   - 系统环境信息
   - 操作步骤记录

2. **检查配置文件**：
   - `.env` 文件配置
   - `vite.config.js` 配置
   - 数据库连接参数

3. **网络诊断**：
   - IP地址和端口检查
   - 防火墙规则验证
   - 跨设备访问测试

---

**🎉 部署完成后，您就可以在家庭局域网内愉快地使用FamilyOAPlatform了！**
