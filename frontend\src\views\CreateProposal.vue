<template>
  <div class="create-proposal-page">
    <!-- Page Header -->
    <div class="page-header">
      <div class="header-content">
        <h1>创建提案</h1>
        <p>提出您的想法和建议</p>
      </div>
      <div class="header-actions">
        <el-button @click="$router.go(-1)">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
      </div>
    </div>

    <!-- Proposal Form -->
    <el-card class="form-card">
      <el-form
        ref="proposalFormRef"
        :model="proposalForm"
        :rules="proposalRules"
        label-width="100px"
        class="proposal-form"
      >
        <el-form-item label="提案标题" prop="title">
          <el-input
            v-model="proposalForm.title"
            placeholder="请输入提案标题"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="提案描述" prop="description">
          <el-input
            v-model="proposalForm.description"
            type="textarea"
            :rows="6"
            placeholder="请详细描述您的提案内容..."
            maxlength="2000"
            show-word-limit
          />
          <div class="ai-actions">
            <el-button
              size="small"
              type="primary"
              text
              :loading="expandingDescription"
              @click="expandDescription"
            >
              <el-icon><MagicStick /></el-icon>
              AI扩展描述
            </el-button>
          </div>
        </el-form-item>

        <el-form-item v-if="expandedDescription" label="AI扩展描述">
          <div class="expanded-content">
            <p>{{ expandedDescription }}</p>
            <div class="expanded-actions">
              <el-button size="small" @click="useExpandedDescription">
                使用此描述
              </el-button>
              <el-button size="small" @click="expandedDescription = ''">
                忽略
              </el-button>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="解决方案" prop="solution">
          <el-input
            v-model="proposalForm.solution"
            type="textarea"
            :rows="6"
            placeholder="请描述您的解决方案..."
            maxlength="2000"
            show-word-limit
          />
          <div class="ai-actions">
            <el-button
              size="small"
              type="primary"
              text
              :loading="expandingSolution"
              @click="expandSolution"
            >
              <el-icon><MagicStick /></el-icon>
              AI扩展方案
            </el-button>
          </div>
        </el-form-item>

        <el-form-item v-if="expandedSolution" label="AI扩展方案">
          <div class="expanded-content">
            <p>{{ expandedSolution }}</p>
            <div class="expanded-actions">
              <el-button size="small" @click="useExpandedSolution">
                使用此方案
              </el-button>
              <el-button size="small" @click="expandedSolution = ''">
                忽略
              </el-button>
            </div>
          </div>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="优先级" prop="priority">
              <el-select v-model="proposalForm.priority" placeholder="选择优先级">
                <el-option label="低" value="low" />
                <el-option label="中" value="medium" />
                <el-option label="高" value="high" />
                <el-option label="紧急" value="urgent" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="截止日期">
              <el-date-picker
                v-model="proposalForm.dueDate"
                type="date"
                placeholder="选择截止日期"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item>
          <div class="form-actions">
            <el-button @click="saveDraft" :loading="saving">
              保存草稿
            </el-button>
            <el-button type="primary" @click="submitProposal" :loading="saving">
              提交提案
            </el-button>
          </div>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, MagicStick } from '@element-plus/icons-vue'
import { useProposalStore } from '../stores/proposal'

const router = useRouter()
const proposalStore = useProposalStore()

const proposalFormRef = ref()
const saving = ref(false)
const expandingDescription = ref(false)
const expandingSolution = ref(false)
const expandedDescription = ref('')
const expandedSolution = ref('')

const proposalForm = reactive({
  title: '',
  description: '',
  solution: '',
  priority: 'medium',
  dueDate: null
})

const proposalRules = {
  title: [
    { required: true, message: '请输入提案标题', trigger: 'blur' },
    { min: 5, max: 200, message: '标题长度在5到200个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入提案描述', trigger: 'blur' },
    { min: 10, max: 2000, message: '描述长度在10到2000个字符', trigger: 'blur' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ]
}

const expandDescription = async () => {
  if (!proposalForm.description.trim()) {
    ElMessage.warning('请先输入提案描述')
    return
  }

  expandingDescription.value = true
  try {
    const result = await proposalStore.expandContent(proposalForm.description, 'description')
    if (result.success) {
      expandedDescription.value = result.data.expandedText
    } else {
      ElMessage.error(result.message)
    }
  } catch (error) {
    ElMessage.error('AI扩展失败')
  } finally {
    expandingDescription.value = false
  }
}

const expandSolution = async () => {
  if (!proposalForm.solution.trim()) {
    ElMessage.warning('请先输入解决方案')
    return
  }

  expandingSolution.value = true
  try {
    const result = await proposalStore.expandContent(proposalForm.solution, 'solution')
    if (result.success) {
      expandedSolution.value = result.data.expandedText
    } else {
      ElMessage.error(result.message)
    }
  } catch (error) {
    ElMessage.error('AI扩展失败')
  } finally {
    expandingSolution.value = false
  }
}

const useExpandedDescription = () => {
  proposalForm.description = expandedDescription.value
  expandedDescription.value = ''
  ElMessage.success('已使用AI扩展描述')
}

const useExpandedSolution = () => {
  proposalForm.solution = expandedSolution.value
  expandedSolution.value = ''
  ElMessage.success('已使用AI扩展方案')
}

const saveDraft = async () => {
  if (!proposalFormRef.value) return

  const valid = await proposalFormRef.value.validate().catch(() => false)
  if (!valid) return

  saving.value = true
  try {
    const result = await proposalStore.createProposal({
      ...proposalForm,
      status: 'draft'
    })

    if (result.success) {
      ElMessage.success('草稿保存成功')
      router.push('/proposals')
    } else {
      ElMessage.error(result.message)
    }
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const submitProposal = async () => {
  if (!proposalFormRef.value) return

  const valid = await proposalFormRef.value.validate().catch(() => false)
  if (!valid) return

  saving.value = true
  try {
    const result = await proposalStore.createProposal({
      ...proposalForm,
      status: 'pending'
    })

    if (result.success) {
      ElMessage.success('提案提交成功')
      router.push('/proposals')
    } else {
      ElMessage.error(result.message)
    }
  } catch (error) {
    ElMessage.error('提交失败')
  } finally {
    saving.value = false
  }
}
</script>

<style scoped>
.create-proposal-page {
  max-width: 1000px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 24px;
}

.header-content h1 {
  font-size: 28px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 8px;
}

.header-content p {
  color: #718096;
  font-size: 16px;
}

.form-card {
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.proposal-form {
  max-width: 800px;
}

.ai-actions {
  margin-top: 8px;
  display: flex;
  gap: 8px;
}

.expanded-content {
  background: #f8f9fa;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 16px;
  margin-top: 8px;
}

.expanded-content p {
  margin-bottom: 12px;
  line-height: 1.6;
  color: #2d3748;
}

.expanded-actions {
  display: flex;
  gap: 8px;
}

.form-actions {
  display: flex;
  gap: 16px;
  padding-top: 16px;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-content h1 {
    font-size: 24px;
  }

  .proposal-form {
    max-width: 100%;
  }

  .form-actions {
    flex-direction: column;
  }

  .form-actions .el-button {
    width: 100%;
  }
}
</style>
