* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 
               'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, 
               Arial, sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #f8f9fa;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Card shadows */
.el-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
  border: none;
  border-radius: 8px;
}

/* Button styles */
.el-button {
  border-radius: 6px;
  font-weight: 500;
}

.el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: #ffffff !important;
}

.el-button--primary:hover,
.el-button--primary:focus,
.el-button--primary:active {
  color: #ffffff !important;
}

.el-button--success {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
  border: none;
  color: #ffffff !important;
}

.el-button--success:hover,
.el-button--success:focus,
.el-button--success:active {
  color: #ffffff !important;
}

.el-button--danger {
  background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
  border: none;
  color: #ffffff !important;
}

.el-button--danger:hover,
.el-button--danger:focus,
.el-button--danger:active {
  color: #ffffff !important;
}

/* Form styles */
.el-form-item__label {
  font-weight: 600;
  color: #2d3748;
}

.el-input__inner,
.el-textarea__inner {
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.el-input__inner:focus,
.el-textarea__inner:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Layout styles */
.main-layout {
  min-height: 100vh;
}

.header-nav {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.sidebar {
  background: white;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.08);
}

.content-area {
  padding: 24px;
  background: #f8f9fa;
  min-height: calc(100vh - 60px);
}

/* Status badges */
.status-badge {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-draft {
  background: #edf2f7;
  color: #4a5568;
}

.status-pending {
  background: #fef5e7;
  color: #d69e2e;
}

.status-approved {
  background: #f0fff4;
  color: #38a169;
}

.status-rejected {
  background: #fed7d7;
  color: #e53e3e;
}

.status-completed {
  background: #e6fffa;
  color: #319795;
}

/* Priority badges */
.priority-badge {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
}

.priority-low {
  background: #e6fffa;
  color: #319795;
}

.priority-medium {
  background: #fef5e7;
  color: #d69e2e;
}

.priority-high {
  background: #fed7d7;
  color: #e53e3e;
}

.priority-urgent {
  background: #fbb6ce;
  color: #b83280;
}

/* Animation classes */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-fade-enter-active {
  transition: all 0.3s ease-out;
}

.slide-fade-leave-active {
  transition: all 0.3s cubic-bezier(1.0, 0.5, 0.8, 1.0);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateX(20px);
  opacity: 0;
}

/* Responsive design */
@media (max-width: 768px) {
  .content-area {
    padding: 16px;
  }
  
  .el-card {
    margin-bottom: 16px;
  }
  
  .el-table {
    font-size: 14px;
  }
}
