{"version": "2.0.0", "tasks": [{"label": "启动家庭办公平台", "type": "shell", "command": "npm", "args": ["run", "dev"], "group": "build", "isBackground": true, "problemMatcher": [], "options": {"cwd": "${workspaceFolder}/backend"}}, {"label": "启动前端服务", "type": "shell", "command": "npm", "args": ["run", "dev"], "options": {"cwd": "${workspaceFolder}/frontend"}, "group": "build", "isBackground": true, "problemMatcher": []}, {"label": "启动完整应用", "dependsOrder": "parallel", "dependsOn": ["启动家庭办公平台", "启动前端服务"]}, {"label": "构建前端应用", "type": "shell", "command": "npm", "args": ["run", "build"], "options": {"cwd": "${workspaceFolder}/frontend"}, "group": "build"}]}