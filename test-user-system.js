const axios = require('axios');

async function testUserSystem() {
  try {
    console.log('测试用户系统简化...');
    
    // 1. 测试新用户登录
    console.log('\n1. 测试新用户登录:');
    
    const users = [
      { username: '<PERSON><PERSON><PERSON><PERSON>', password: '<PERSON><PERSON><PERSON><PERSON>', name: '<PERSON>凡皓' },
      { username: '<PERSON><PERSON><PERSON><PERSON><PERSON>', password: '<PERSON><PERSON><PERSON><PERSON><PERSON>', name: '林卓恒' },
      { username: '<PERSON><PERSON><PERSON><PERSON>', password: '<PERSON>u<PERSON><PERSON>', name: '区敏' }
    ];
    
    for (const user of users) {
      try {
        const loginResponse = await axios.post('http://localhost:3002/api/auth/login', {
          username: user.username,
          password: user.password
        });
        
        console.log(`✅ ${user.name} (${user.username}) 登录成功`);
        console.log(`   - 用户ID: ${loginResponse.data.user.id}`);
        console.log(`   - 真实姓名: ${loginResponse.data.user.realName}`);
        console.log(`   - 无邮箱字段: ${!loginResponse.data.user.email ? '✅' : '❌'}`);
        console.log(`   - 无角色字段: ${!loginResponse.data.user.role ? '✅' : '❌'}`);
        
        // 2. 测试获取用户信息
        const token = loginResponse.data.token;
        const userInfoResponse = await axios.get('http://localhost:3002/api/auth/me', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        
        console.log(`   - 获取用户信息成功: ✅`);
        console.log(`   - 用户信息无邮箱: ${!userInfoResponse.data.user.email ? '✅' : '❌'}`);
        console.log(`   - 用户信息无角色: ${!userInfoResponse.data.user.role ? '✅' : '❌'}`);
        
      } catch (error) {
        console.log(`❌ ${user.name} (${user.username}) 登录失败:`, error.response?.data?.error || error.message);
      }
    }
    
    // 3. 测试用户列表（无管理员权限检查）
    console.log('\n3. 测试用户列表访问:');
    const loginResponse = await axios.post('http://localhost:3002/api/auth/login', {
      username: 'LinFanhao',
      password: 'LinFanhao'
    });
    
    const token = loginResponse.data.token;
    const usersResponse = await axios.get('http://localhost:3002/api/users', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log(`✅ 普通用户可以访问用户列表`);
    console.log(`   - 用户数量: ${usersResponse.data.length}`);
    usersResponse.data.forEach(user => {
      console.log(`   - ${user.nickname} (${user.username})`);
      console.log(`     * 无邮箱字段: ${!user.email ? '✅' : '❌'}`);
      console.log(`     * 无角色字段: ${!user.role ? '✅' : '❌'}`);
    });
    
    // 4. 测试注册新用户（无邮箱）
    console.log('\n4. 测试注册新用户（无邮箱）:');
    try {
      const registerResponse = await axios.post('http://localhost:3002/api/auth/register', {
        username: 'TestUser',
        password: 'TestPassword',
        realName: '测试用户'
      });
      
      console.log(`✅ 注册成功（无邮箱字段）`);
      console.log(`   - 用户ID: ${registerResponse.data.userId}`);
      
      // 测试新注册用户登录
      const newUserLogin = await axios.post('http://localhost:3002/api/auth/login', {
        username: 'TestUser',
        password: 'TestPassword'
      });
      
      console.log(`✅ 新注册用户登录成功`);
      console.log(`   - 无邮箱字段: ${!newUserLogin.data.user.email ? '✅' : '❌'}`);
      console.log(`   - 无角色字段: ${!newUserLogin.data.user.role ? '✅' : '❌'}`);
      
    } catch (error) {
      console.log(`❌ 注册失败:`, error.response?.data?.error || error.message);
    }
    
    console.log('\n🎉 用户系统简化测试完成！');
    console.log('📝 简化内容:');
    console.log('  ✅ 移除了邮箱字段');
    console.log('  ✅ 移除了角色字段');
    console.log('  ✅ 移除了管理员权限检查');
    console.log('  ✅ 所有用户权限相同');
    console.log('  ✅ 使用中文姓名和拼音用户名');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
  }
}

testUserSystem();
