# 部署说明 (Deployment Guide)

## 系统要求

- **操作系统**: Windows 10/11, macOS, Linux
- **Node.js**: 版本 >= 16.0.0
- **MySQL**: 版本 >= 8.0
- **内存**: 最少 4GB RAM
- **存储**: 最少 2GB 可用空间

## 开发环境部署

### 1. 环境准备

确保已安装以下软件：
- Node.js (推荐 LTS 版本)
- MySQL 数据库
- Git (可选)

### 2. 获取源码

```bash
# 如果有 Git 仓库
git clone <repository-url>
cd FamilyOAPlatform

# 或者直接解压源码包到目标目录
```

### 3. 数据库配置

#### 创建数据库
```sql
CREATE DATABASE family_oa_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

#### 导入数据库架构
```bash
# 在项目根目录执行
mysql -u root -p family_oa_platform < database/schema.sql
```

### 4. 后端配置

```bash
# 进入后端目录
cd backend

# 安装依赖
npm install

# 复制环境配置文件
copy .env.example .env  # Windows
cp .env.example .env    # macOS/Linux

# 编辑 .env 文件，配置数据库和API密钥
```

#### 环境变量配置 (.env)
```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=family_oa_platform
DB_USER=root
DB_PASSWORD=你的数据库密码

# 服务器配置
PORT=3000
NODE_ENV=development
FRONTEND_URL=http://localhost:5173

# JWT 配置
JWT_SECRET=你的JWT密钥
JWT_EXPIRES_IN=7d

# Google AI 配置
GOOGLE_AI_API_KEY=你的Google_AI_API密钥
GOOGLE_AI_MODEL=gemini-2.0-flash-exp
```

### 5. 前端配置

```bash
# 进入前端目录
cd frontend

# 安装依赖  
npm install
```

### 6. 获取 Google AI API 密钥

1. 访问 [Google AI Studio](https://makersuite.google.com/app/apikey)
2. 登录 Google 账户
3. 创建新的 API 密钥
4. 将密钥复制到 `.env` 文件中的 `GOOGLE_AI_API_KEY`

### 7. 启动应用

#### 方式一：使用 VS Code 任务
1. 在 VS Code 中打开项目
2. 按 `Ctrl+Shift+P` (Windows) 或 `Cmd+Shift+P` (macOS)
3. 输入 "Tasks: Run Task"
4. 选择 "启动完整应用"

#### 方式二：命令行启动
```bash
# 启动后端 (新终端窗口)
cd backend
npm run dev

# 启动前端 (新终端窗口)
cd frontend  
npm run dev
```

### 8. 访问应用

- **前端应用**: http://localhost:5173
- **后端API**: http://localhost:3000
- **健康检查**: http://localhost:3000/health

### 9. 默认账户

系统初始化时会创建默认账户：

| 用户类型 | 用户名 | 密码 | 邮箱 |
|---------|--------|------|------|
| 管理员 | admin | admin123 | <EMAIL> |
| 普通用户 | member | member123 | <EMAIL> |

## 生产环境部署

### 1. 服务器要求

- **CPU**: 2核心以上
- **内存**: 4GB RAM 以上
- **存储**: 10GB 以上可用空间
- **网络**: 宽带连接

### 2. 环境配置

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y  # Ubuntu/Debian
# 或
sudo yum update -y  # CentOS/RHEL

# 安装 Node.js
curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装 MySQL
sudo apt install mysql-server -y
sudo mysql_secure_installation

# 安装 PM2 (进程管理器)
sudo npm install -g pm2
```

### 3. 应用部署

```bash
# 克隆代码到服务器
git clone <repository-url> /opt/family-oa-platform
cd /opt/family-oa-platform

# 设置文件权限
sudo chown -R $USER:$USER /opt/family-oa-platform

# 后端部署
cd backend
npm install --production
cp .env.example .env
# 编辑 .env 配置生产环境参数

# 前端构建
cd ../frontend
npm install
npm run build

# 使用 PM2 启动后端服务
cd ../backend
pm2 start ecosystem.config.js --env production
pm2 save
pm2 startup
```

### 4. Nginx 反向代理配置

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 前端静态文件
    location / {
        root /opt/family-oa-platform/frontend/dist;
        index index.html;
        try_files $uri $uri/ /index.html;
    }
    
    # 后端API代理
    location /api {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### 5. SSL证书配置 (可选)

```bash
# 安装 Certbot
sudo apt install certbot python3-certbot-nginx -y

# 获取SSL证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 家庭网络部署

### 1. 路由器配置

1. **端口转发设置**:
   - 内部IP: 192.168.x.x (你的电脑IP)
   - 外部端口: 80, 443
   - 内部端口: 80, 443

2. **动态DNS设置** (可选):
   - 使用花生壳、DDNS等服务
   - 获得固定域名访问

### 2. 防火墙配置

```bash
# Windows 防火墙
# 控制面板 -> 系统和安全 -> Windows Defender 防火墙
# 允许应用或功能通过防火墙 -> 添加端口 80, 443, 3000, 5173

# Linux 防火墙 (ufw)
sudo ufw allow 80
sudo ufw allow 443
sudo ufw allow 3000
sudo ufw allow 5173
sudo ufw enable
```

### 3. 内网访问

在同一WiFi网络下的设备可通过以下方式访问：
- http://192.168.x.x:5173 (直接访问前端)
- http://192.168.x.x (如果配置了Nginx)

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查MySQL服务是否启动
   - 确认数据库用户权限
   - 验证 .env 文件中的数据库配置

2. **前端无法访问后端**
   - 检查后端服务是否正常运行
   - 确认端口是否被占用
   - 检查防火墙设置

3. **AI功能不可用**
   - 验证 Google AI API 密钥是否正确
   - 检查网络连接是否正常
   - 确认API配额是否充足

### 日志查看

```bash
# PM2 日志
pm2 logs

# 系统日志
sudo journalctl -u nginx -f  # Nginx日志
sudo tail -f /var/log/mysql/error.log  # MySQL日志
```

### 性能优化

1. **数据库优化**
   - 定期清理日志
   - 优化查询索引
   - 配置适当的连接池大小

2. **应用优化**
   - 启用 Gzip 压缩
   - 配置静态资源缓存
   - 使用CDN加速 (可选)

## 维护指南

### 定期维护任务

1. **数据备份** (建议每日)
```bash
mysqldump -u root -p family_oa_platform > backup_$(date +%Y%m%d).sql
```

2. **日志清理** (建议每周)
```bash
pm2 flush  # 清理PM2日志
sudo logrotate -f /etc/logrotate.conf  # 系统日志轮转
```

3. **系统更新** (建议每月)
```bash
sudo apt update && sudo apt upgrade -y
npm update  # 更新依赖包
```

### 监控建议

- 使用 PM2 监控应用状态
- 配置数据库性能监控
- 设置磁盘空间告警
- 监控网络流量使用

---

如需帮助，请参考项目 README.md 文件或提交 Issue。
