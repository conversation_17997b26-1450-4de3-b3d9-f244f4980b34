<template>
  <div class="proposals-page">
    <!-- Page Header -->
    <div class="page-header">
      <div class="header-content">
        <h1>提案管理</h1>
        <p>管理和跟踪所有家庭提案</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="$router.push('/proposals/create')">
          <el-icon><Plus /></el-icon>
          创建提案
        </el-button>
      </div>
    </div>

    <!-- Filters -->
    <el-card class="filters-card">
      <el-form inline class="filters-form">
        <el-form-item label="状态筛选">
          <el-select
            v-model="filters.status"
            placeholder="选择状态"
            clearable
            @change="handleFilterChange"
          >
            <el-option label="草稿" value="draft" />
            <el-option label="待处理" value="pending" />
            <el-option label="已通过" value="approved" />
            <el-option label="已拒绝" value="rejected" />
            <el-option label="已完成" value="completed" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="优先级">
          <el-select
            v-model="filters.priority"
            placeholder="选择优先级"
            clearable
            @change="handleFilterChange"
          >
            <el-option label="低" value="low" />
            <el-option label="中" value="medium" />
            <el-option label="高" value="high" />
            <el-option label="紧急" value="urgent" />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button @click="resetFilters">重置</el-button>
          <el-button type="primary" @click="loadProposals">刷新</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- Proposals Table -->
    <el-card class="table-card">
      <el-table
        v-loading="proposalStore.loading"
        :data="proposalStore.proposals"
        @row-click="handleRowClick"
        class="proposals-table"
      >
        <el-table-column prop="title" label="标题" min-width="200">
          <template #default="{ row }">
            <div class="proposal-title">
              {{ row.title }}
              <el-tag v-if="row.priority === 'urgent'" type="danger" size="small">
                紧急
              </el-tag>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="description" label="描述" min-width="250">
          <template #default="{ row }">
            <div class="description-cell">
              {{ row.description }}
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="creator_name" label="创建人" width="100" />
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="priority" label="优先级" width="100">
          <template #default="{ row }">
            <el-tag :type="getPriorityTagType(row.priority)" size="small">
              {{ getPriorityText(row.priority) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="统计" width="120">
          <template #default="{ row }">
            <div class="stats-cell">
              <span class="stat-item">
                <el-icon><ChatDotRound /></el-icon>
                {{ row.comment_count || 0 }}
              </span>
              <span class="stat-item">
                <el-icon><Select /></el-icon>
                {{ (row.approve_votes || 0) + (row.reject_votes || 0) }}
              </span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="created_at" label="创建时间" width="120">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                type="primary"
                size="small"
                text
                @click.stop="viewProposal(row.id)"
              >
                查看
              </el-button>
              
              <el-button
                v-if="canEdit(row)"
                type="warning"
                size="small"
                text
                @click.stop="editProposal(row.id)"
              >
                编辑
              </el-button>
              
              <el-button
                v-if="canDelete(row)"
                type="danger"
                size="small"
                text
                @click.stop="deleteProposal(row)"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- Pagination -->
      <div class="pagination-container">        <el-pagination
          :current-page="pagination.page"
          :page-size="pagination.limit"
          :total="proposalStore.pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, ChatDotRound, Select } from '@element-plus/icons-vue'
import { useAuthStore } from '../stores/auth'
import { useProposalStore } from '../stores/proposal'

const router = useRouter()
const authStore = useAuthStore()
const proposalStore = useProposalStore()

const filters = reactive({
  status: '',
  priority: ''
})

const pagination = reactive({
  page: 1,
  limit: 10
})

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const getStatusText = (status) => {
  const statusMap = {
    draft: '草稿',
    pending: '待处理',
    approved: '已通过',
    rejected: '已拒绝',
    completed: '已完成'
  }
  return statusMap[status] || status
}

const getStatusTagType = (status) => {
  const typeMap = {
    draft: 'info',
    pending: 'warning',
    approved: 'success',
    rejected: 'danger',
    completed: 'success'
  }
  return typeMap[status] || 'info'
}

const getPriorityText = (priority) => {
  const priorityMap = {
    low: '低',
    medium: '中',
    high: '高',
    urgent: '紧急'
  }
  return priorityMap[priority] || priority
}

const getPriorityTagType = (priority) => {
  const typeMap = {
    low: 'success',
    medium: 'warning',
    high: 'danger',
    urgent: 'danger'
  }
  return typeMap[priority] || 'info'
}

const canEdit = (proposal) => {
  return authStore.user?.id === proposal.creator_id || authStore.isAdmin
}

const canDelete = (proposal) => {
  return authStore.user?.id === proposal.creator_id || authStore.isAdmin
}

const handleRowClick = (row) => {
  viewProposal(row.id)
}

const viewProposal = (id) => {
  router.push(`/proposals/${id}`)
}

const editProposal = (id) => {
  router.push(`/proposals/${id}/edit`)
}

const deleteProposal = async (proposal) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除提案"${proposal.title}"吗？此操作不可撤销。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const result = await proposalStore.deleteProposal(proposal.id)
    
    if (result.success) {
      ElMessage.success(result.message)
    } else {
      ElMessage.error(result.message)
    }
  } catch {
    // User cancelled
  }
}

const handleFilterChange = () => {
  pagination.page = 1
  loadProposals()
}

const resetFilters = () => {
  filters.status = ''
  filters.priority = ''
  pagination.page = 1
  loadProposals()
}

const handleSizeChange = (val) => {
  pagination.limit = val
  pagination.page = 1
  loadProposals()
}

const handleCurrentChange = (val) => {
  pagination.page = val
  loadProposals()
}

const loadProposals = async () => {
  const params = {
    page: pagination.page,
    limit: pagination.limit
  }
  
  if (filters.status) {
    params.status = filters.status
  }
  
  if (filters.priority) {
    params.priority = filters.priority
  }
  
  const result = await proposalStore.fetchProposals(params)
  
  if (!result.success) {
    ElMessage.error(result.message)
  }
}

onMounted(() => {
  loadProposals()
})
</script>

<style scoped>
.proposals-page {
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 24px;
}

.header-content h1 {
  font-size: 28px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 8px;
}

.header-content p {
  color: #718096;
  font-size: 16px;
}

.filters-card {
  margin-bottom: 24px;
}

.filters-form {
  margin: 0;
}

.table-card {
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.proposals-table {
  width: 100%;
}

.proposals-table :deep(.el-table__row) {
  cursor: pointer;
  transition: background-color 0.3s;
}

.proposals-table :deep(.el-table__row:hover) {
  background-color: #f8f9fa;
}

.proposal-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.description-cell {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  line-height: 1.4;
  max-height: 2.8em;
  word-break: break-word;
}

.stats-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #718096;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.pagination-container {
  margin-top: 24px;
  display: flex;
  justify-content: center;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .header-content h1 {
    font-size: 24px;
  }
  
  .filters-form {
    flex-direction: column;
  }
  
  .filters-form .el-form-item {
    margin-right: 0;
    margin-bottom: 16px;
  }
  
  .proposals-table {
    font-size: 14px;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }
}
</style>
