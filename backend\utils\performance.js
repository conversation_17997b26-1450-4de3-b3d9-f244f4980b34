const compression = require('compression');
const { createHash } = require('crypto');

// 响应压缩中间件
const compressionMiddleware = compression({
  filter: (req, res) => {
    // 不压缩已经压缩的内容
    if (req.headers['x-no-compression']) {
      return false;
    }
    
    // 使用compression的默认过滤器
    return compression.filter(req, res);
  },
  level: 6, // 压缩级别 (1-9)
  threshold: 1024, // 只压缩大于1KB的响应
  memLevel: 8 // 内存级别 (1-9)
});

// 简单的内存缓存实现
class MemoryCache {
  constructor(maxSize = 100, ttl = 300000) { // 默认5分钟TTL
    this.cache = new Map();
    this.maxSize = maxSize;
    this.ttl = ttl;
  }
  
  generateKey(req) {
    const keyData = {
      url: req.originalUrl,
      method: req.method,
      userId: req.user?.userId || 'anonymous',
      query: req.query
    };
    
    return createHash('md5')
      .update(JSON.stringify(keyData))
      .digest('hex');
  }
  
  get(key) {
    const item = this.cache.get(key);
    
    if (!item) return null;
    
    // 检查是否过期
    if (Date.now() > item.expiry) {
      this.cache.delete(key);
      return null;
    }
    
    return item.data;
  }
  
  set(key, data) {
    // 如果缓存已满，删除最旧的项
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    
    this.cache.set(key, {
      data,
      expiry: Date.now() + this.ttl
    });
  }
  
  clear() {
    this.cache.clear();
  }
  
  size() {
    return this.cache.size;
  }
}

// 创建缓存实例
const apiCache = new MemoryCache(50, 300000); // 50个项目，5分钟TTL

// 缓存中间件
const cacheMiddleware = (ttl = 300000) => {
  return (req, res, next) => {
    // 只缓存GET请求
    if (req.method !== 'GET') {
      return next();
    }
    
    const key = apiCache.generateKey(req);
    const cachedData = apiCache.get(key);
    
    if (cachedData) {
      console.log(`Cache hit for key: ${key}`);
      return res.json(cachedData);
    }
    
    // 重写res.json以缓存响应
    const originalJson = res.json;
    res.json = function(data) {
      // 只缓存成功的响应
      if (res.statusCode === 200) {
        apiCache.set(key, data);
        console.log(`Cache set for key: ${key}`);
      }
      
      return originalJson.call(this, data);
    };
    
    next();
  };
};

// 数据库查询优化工具
class QueryOptimizer {
  static buildPaginationQuery(baseQuery, page = 1, limit = 10, sortBy = 'created_at', sortOrder = 'DESC') {
    const safeLimit = Math.max(1, Math.min(100, parseInt(limit) || 10));
    const safeOffset = Math.max(0, (parseInt(page) - 1) * safeLimit);
    const safeSortBy = this.sanitizeSortField(sortBy);
    const safeSortOrder = sortOrder.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';
    
    return {
      query: `${baseQuery} ORDER BY ${safeSortBy} ${safeSortOrder} LIMIT ${safeLimit} OFFSET ${safeOffset}`,
      limit: safeLimit,
      offset: safeOffset,
      page: parseInt(page)
    };
  }
  
  static sanitizeSortField(field) {
    const allowedFields = [
      'id', 'title', 'status', 'priority', 'created_at', 'updated_at',
      'username', 'email', 'real_name', 'role'
    ];
    
    return allowedFields.includes(field) ? field : 'created_at';
  }
  
  static buildSearchQuery(baseQuery, searchTerm, searchFields = ['title', 'description']) {
    if (!searchTerm || searchTerm.trim().length === 0) {
      return { query: baseQuery, params: [] };
    }
    
    const searchConditions = searchFields.map(field => `${field} LIKE ?`).join(' OR ');
    const searchValue = `%${searchTerm.trim()}%`;
    const params = new Array(searchFields.length).fill(searchValue);
    
    const whereClause = baseQuery.includes('WHERE') 
      ? ` AND (${searchConditions})`
      : ` WHERE (${searchConditions})`;
    
    return {
      query: baseQuery + whereClause,
      params
    };
  }
  
  static buildFilterQuery(baseQuery, filters = {}) {
    let query = baseQuery;
    const params = [];
    
    Object.entries(filters).forEach(([field, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        const whereClause = query.includes('WHERE') ? ' AND' : ' WHERE';
        
        if (Array.isArray(value)) {
          const placeholders = value.map(() => '?').join(',');
          query += `${whereClause} ${field} IN (${placeholders})`;
          params.push(...value);
        } else {
          query += `${whereClause} ${field} = ?`;
          params.push(value);
        }
      }
    });
    
    return { query, params };
  }
}

// 性能监控中间件
const performanceMonitor = (req, res, next) => {
  const start = process.hrtime.bigint();
  
  res.on('finish', () => {
    const end = process.hrtime.bigint();
    const duration = Number(end - start) / 1000000; // 转换为毫秒
    
    // 记录慢查询（超过1秒）
    if (duration > 1000) {
      console.warn(`Slow request detected: ${req.method} ${req.originalUrl} - ${duration.toFixed(2)}ms`);
    }
    
    // 记录性能指标
    if (process.env.NODE_ENV === 'development') {
      console.log(`Performance: ${req.method} ${req.originalUrl} - ${duration.toFixed(2)}ms`);
    }
  });
  
  next();
};

// 批量操作工具
class BatchProcessor {
  static async processBatch(items, processor, batchSize = 10) {
    const results = [];
    
    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize);
      const batchResults = await Promise.all(
        batch.map(item => processor(item))
      );
      results.push(...batchResults);
    }
    
    return results;
  }
  
  static async processSequential(items, processor) {
    const results = [];
    
    for (const item of items) {
      const result = await processor(item);
      results.push(result);
    }
    
    return results;
  }
}

// 响应时间统计
class ResponseTimeStats {
  constructor() {
    this.stats = new Map();
  }
  
  record(endpoint, duration) {
    if (!this.stats.has(endpoint)) {
      this.stats.set(endpoint, {
        count: 0,
        total: 0,
        min: Infinity,
        max: 0,
        avg: 0
      });
    }
    
    const stat = this.stats.get(endpoint);
    stat.count++;
    stat.total += duration;
    stat.min = Math.min(stat.min, duration);
    stat.max = Math.max(stat.max, duration);
    stat.avg = stat.total / stat.count;
  }
  
  getStats(endpoint) {
    return this.stats.get(endpoint) || null;
  }
  
  getAllStats() {
    return Object.fromEntries(this.stats);
  }
  
  reset() {
    this.stats.clear();
  }
}

const responseTimeStats = new ResponseTimeStats();

// 清理过期缓存的定时任务
setInterval(() => {
  const sizeBefore = apiCache.size();
  // 触发缓存清理（通过尝试获取一个不存在的key）
  apiCache.get('__cleanup__');
  const sizeAfter = apiCache.size();
  
  if (sizeBefore !== sizeAfter) {
    console.log(`Cache cleanup: ${sizeBefore - sizeAfter} expired items removed`);
  }
}, 60000); // 每分钟清理一次

module.exports = {
  compressionMiddleware,
  cacheMiddleware,
  performanceMonitor,
  QueryOptimizer,
  BatchProcessor,
  ResponseTimeStats,
  responseTimeStats,
  apiCache
};
