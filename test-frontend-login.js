const axios = require('axios');

async function testFrontendLogin() {
  try {
    console.log('测试前端登录功能...');
    
    // 模拟前端登录请求
    const response = await axios.post('http://localhost:3002/api/auth/login', {
      username: 'lin<PERSON><PERSON>',
      password: 'lin<PERSON><PERSON>'
    });
    
    console.log('登录成功!');
    console.log('响应数据:', response.data);
    
    // 测试获取用户信息
    const token = response.data.token;
    const userResponse = await axios.get('http://localhost:3002/api/auth/me', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log('用户信息:', userResponse.data);
    
  } catch (error) {
    console.error('测试失败:', error.response?.data || error.message);
  }
}

testFrontendLogin();
