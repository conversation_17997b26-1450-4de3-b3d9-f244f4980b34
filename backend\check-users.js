const mysql = require('mysql2/promise');

async function checkUsers() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: '123456',
    database: 'family_oa_platform'
  });

  try {
    console.log('检查用户数据...');

    // 1. 检查用户表结构
    console.log('\n1. 检查用户表结构:');
    const [columns] = await connection.execute('DESCRIBE users');
    console.log('用户表字段:');
    columns.forEach(col => {
      console.log(`  - ${col.Field}: ${col.Type} ${col.Null === 'NO' ? 'NOT NULL' : 'NULL'} ${col.Key ? `(${col.Key})` : ''}`);
    });

    // 2. 检查用户数据
    console.log('\n2. 检查用户数据:');
    const [users] = await connection.execute('SELECT * FROM users');
    console.log(`用户数量: ${users.length}`);
    
    if (users.length > 0) {
      users.forEach(user => {
        console.log(`  - ID: ${user.id}`);
        console.log(`    用户名: ${user.username}`);
        console.log(`    真实姓名: ${user.real_name}`);
        console.log(`    密码哈希: ${user.password_hash ? '已设置' : '未设置'}`);
        console.log(`    创建时间: ${user.created_at}`);
        console.log('');
      });
    } else {
      console.log('  没有用户数据');
    }

    // 3. 测试密码验证
    console.log('3. 测试密码验证:');
    const bcrypt = require('bcryptjs');
    
    for (const user of users) {
      const testPasswords = [user.username, user.username.toLowerCase(), user.username.toUpperCase()];
      
      for (const testPassword of testPasswords) {
        const isValid = await bcrypt.compare(testPassword, user.password_hash);
        if (isValid) {
          console.log(`  ✅ 用户 ${user.username} 的密码是: ${testPassword}`);
          break;
        }
      }
    }

  } catch (error) {
    console.error('❌ 检查失败:', error);
  } finally {
    await connection.end();
  }
}

checkUsers();
