const axios = require('axios');

async function testIPAccess() {
  console.log('🌐 IP访问功能测试');
  console.log('=' .repeat(50));
  
  // 测试不同的访问方式
  const testUrls = [
    'http://localhost:3002/api',
    'http://127.0.0.1:3002/api', 
    'http://*************:3002/api'
  ];
  
  for (const baseUrl of testUrls) {
    console.log(`\n🔍 测试后端访问: ${baseUrl}`);
    
    try {
      // 测试健康检查
      const healthResponse = await axios.get(`${baseUrl.replace('/api', '')}/health`, {
        timeout: 5000
      });
      
      console.log(`✅ 健康检查成功: ${healthResponse.data.message}`);
      
      // 测试登录功能
      try {
        const loginResponse = await axios.post(`${baseUrl}/auth/login`, {
          username: '<PERSON><PERSON><PERSON><PERSON>',
          password: '<PERSON><PERSON><PERSON><PERSON>'
        }, {
          timeout: 10000,
          headers: {
            'Content-Type': 'application/json'
          }
        });
        
        console.log(`✅ 登录测试成功: ${loginResponse.data.user.realName}`);
        
        // 测试需要认证的API
        const token = loginResponse.data.token;
        const proposalsResponse = await axios.get(`${baseUrl}/proposals?limit=3`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          timeout: 10000
        });
        
        console.log(`✅ 认证API测试成功: 获取到${proposalsResponse.data.length}个提案`);
        
      } catch (authError) {
        console.log(`❌ 认证测试失败: ${authError.response?.data?.error || authError.message}`);
      }
      
    } catch (error) {
      console.log(`❌ 连接失败: ${error.message}`);
      if (error.code === 'ECONNREFUSED') {
        console.log('   原因: 连接被拒绝，服务器可能未启动或未绑定到此地址');
      } else if (error.code === 'ENOTFOUND') {
        console.log('   原因: 无法解析主机名');
      } else if (error.code === 'ETIMEDOUT') {
        console.log('   原因: 连接超时');
      }
    }
  }
  
  console.log('\n📋 测试总结');
  console.log('=' .repeat(50));
  console.log('');
  console.log('🔧 已完成的修复:');
  console.log('   ✅ 后端服务器绑定到 0.0.0.0:3002 (允许外部访问)');
  console.log('   ✅ 前端服务器绑定到 0.0.0.0:5173 (允许外部访问)');
  console.log('   ✅ CORS配置支持本地网络IP访问');
  console.log('   ✅ 前端API配置动态适配访问地址');
  console.log('');
  console.log('🌐 现在可以通过以下方式访问:');
  console.log('   - http://localhost:5173 (本地访问)');
  console.log('   - http://127.0.0.1:5173 (本地回环)');
  console.log('   - http://*************:5173 (局域网IP访问)');
  console.log('');
  console.log('📱 移动设备访问:');
  console.log('   - 确保设备连接到同一WiFi网络');
  console.log('   - 使用 http://*************:5173 访问');
  console.log('');
  console.log('🔒 安全说明:');
  console.log('   - 当前配置仅适用于开发环境');
  console.log('   - 生产环境需要额外的安全配置');
  console.log('   - 建议在生产环境中使用HTTPS和域名');
}

testIPAccess();
