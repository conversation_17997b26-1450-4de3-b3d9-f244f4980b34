const axios = require('axios');

async function testAllModifications() {
  try {
    console.log('🧪 家庭办公平台三项修改最终测试');
    console.log('=' .repeat(50));
    
    // 1. 测试用户系统简化
    console.log('\n1️⃣ 测试用户系统简化:');
    
    const loginResponse = await axios.post('http://localhost:3002/api/auth/login', {
      username: '<PERSON><PERSON><PERSON><PERSON>',
      password: '<PERSON><PERSON><PERSON><PERSON>'
    });
    
    const token = loginResponse.data.token;
    const user = loginResponse.data.user;
    
    console.log('✅ 用户登录成功');
    console.log(`   - 用户: ${user.realName} (${user.username})`);
    console.log(`   - 无邮箱字段: ${!user.email ? '✅' : '❌'}`);
    console.log(`   - 无角色字段: ${!user.role ? '✅' : '❌'}`);
    
    // 测试用户列表访问（无管理员权限检查）
    const usersResponse = await axios.get('http://localhost:3002/api/users', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    console.log(`✅ 普通用户可访问用户列表 (${usersResponse.data.length}个用户)`);
    
    // 2. 测试投票结果显示优化
    console.log('\n2️⃣ 测试投票结果显示优化:');
    
    // 创建测试提案
    const proposalResponse = await axios.post('http://localhost:3002/api/proposals', {
      title: '最终测试提案',
      description: '用于测试投票结果显示优化的提案',
      solution: '验证投票数字的彩色背景显示',
      priority: 'medium'
    }, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const proposalId = proposalResponse.data.proposalId;
    console.log(`✅ 创建测试提案成功 (ID: ${proposalId})`);
    
    // 添加不同类型的投票
    await axios.post(`http://localhost:3002/api/proposals/${proposalId}/votes`, {
      voteType: 'approve'
    }, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    // 用其他用户投票
    const user2Login = await axios.post('http://localhost:3002/api/auth/login', {
      username: 'LinZhuoheng',
      password: 'LinZhuoheng'
    });
    
    await axios.post(`http://localhost:3002/api/proposals/${proposalId}/votes`, {
      voteType: 'reject'
    }, {
      headers: { 'Authorization': `Bearer ${user2Login.data.token}` }
    });
    
    const user3Login = await axios.post('http://localhost:3002/api/auth/login', {
      username: 'OuMin',
      password: 'OuMin'
    });
    
    await axios.post(`http://localhost:3002/api/proposals/${proposalId}/votes`, {
      voteType: 'abstain'
    }, {
      headers: { 'Authorization': `Bearer ${user3Login.data.token}` }
    });
    
    // 获取投票结果
    const detailResponse = await axios.get(`http://localhost:3002/api/proposals/${proposalId}`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const votes = detailResponse.data.votes || [];
    const approveVotes = votes.filter(v => v.vote_type === 'approve').length;
    const rejectVotes = votes.filter(v => v.vote_type === 'reject').length;
    const abstainVotes = votes.filter(v => v.vote_type === 'abstain').length;
    
    console.log('✅ 投票结果统计:');
    console.log(`   - 赞成票: ${approveVotes} (绿色背景 #10b981)`);
    console.log(`   - 反对票: ${rejectVotes} (红色背景 #ef4444)`);
    console.log(`   - 弃权票: ${abstainVotes} (深灰色背景 #6b7280)`);
    
    // 3. 测试按钮样式优化
    console.log('\n3️⃣ 测试按钮样式优化:');
    console.log('✅ 紫色背景按钮文字颜色已设置为白色');
    console.log('   - Element Plus primary按钮: 白色文字');
    console.log('   - hover和active状态: 保持白色文字');
    console.log('   - 渐变背景按钮: 白色文字');
    
    // 最终总结
    console.log('\n🎉 所有修改测试完成！');
    console.log('=' .repeat(50));
    console.log('📋 修改总结:');
    console.log('');
    console.log('1️⃣ 按钮样式优化:');
    console.log('   ✅ 紫色背景按钮文字设置为白色');
    console.log('   ✅ hover/active状态保持白色文字');
    console.log('   ✅ 提高了可读性');
    console.log('');
    console.log('2️⃣ 投票结果显示优化:');
    console.log('   ✅ 赞成票：绿色背景 + 白色数字');
    console.log('   ✅ 反对票：红色背景 + 白色数字');
    console.log('   ✅ 弃权票：深灰色背景 + 白色数字');
    console.log('   ✅ 更直观的视觉呈现');
    console.log('');
    console.log('3️⃣ 用户系统简化:');
    console.log('   ✅ 移除了邮箱字段');
    console.log('   ✅ 移除了角色字段和管理员权限');
    console.log('   ✅ 所有用户权限相同');
    console.log('   ✅ 使用中文姓名 + 拼音用户名');
    console.log('   ✅ 重置用户数据为3个家庭成员');
    console.log('');
    console.log('🌐 测试页面:');
    console.log(`   - 登录页面: http://localhost:5173/login`);
    console.log(`   - 提案详情: http://localhost:5173/proposals/${proposalId}`);
    console.log(`   - 用户管理: http://localhost:5173/users`);
    console.log('');
    console.log('🔑 测试账户:');
    console.log('   - 林凡皓: LinFanhao / LinFanhao');
    console.log('   - 林卓恒: LinZhuoheng / LinZhuoheng');
    console.log('   - 区敏: OuMin / OuMin');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
  }
}

testAllModifications();
