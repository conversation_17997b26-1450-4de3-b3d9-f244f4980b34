import { defineStore } from 'pinia'
import axios from 'axios'

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3002/api'

export const useAuthStore = defineStore('auth', {
  state: () => ({
    user: null,
    token: localStorage.getItem('auth_token'),
    isAuthenticated: false,
    loading: false
  }),

  getters: {
    // 移除管理员权限检查，所有用户权限相同
  },

  actions: {
    async login(credentials) {
      this.loading = true
      try {
        const response = await axios.post(`${API_BASE_URL}/auth/login`, credentials)
        const { token, user } = response.data
        
        this.token = token
        this.user = user
        this.isAuthenticated = true
        
        localStorage.setItem('auth_token', token)
        
        // Set default axios authorization header
        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`
        
        return { success: true, message: response.data.message }
      } catch (error) {
        const message = error.response?.data?.error || '登录失败'
        return { success: false, message }
      } finally {
        this.loading = false
      }
    },

    async register(userData) {
      this.loading = true
      try {
        const response = await axios.post(`${API_BASE_URL}/auth/register`, userData)
        return { success: true, message: response.data.message }
      } catch (error) {
        const message = error.response?.data?.error || '注册失败'
        return { success: false, message }
      } finally {
        this.loading = false
      }
    },

    async fetchUser() {
      if (!this.token) return
      
      try {
        axios.defaults.headers.common['Authorization'] = `Bearer ${this.token}`
        const response = await axios.get(`${API_BASE_URL}/auth/me`)
        this.user = response.data.user
        this.isAuthenticated = true
      } catch (error) {
        this.logout()
        throw error
      }
    },

    async updateProfile(profileData) {
      try {
        const response = await axios.put(`${API_BASE_URL}/users/profile`, profileData)
        
        // Update local user data
        if (this.user) {
          Object.assign(this.user, profileData)
        }
        
        return { success: true, message: response.data.message }
      } catch (error) {
        const message = error.response?.data?.error || '更新个人资料失败'
        return { success: false, message }
      }
    },

    logout() {
      this.user = null
      this.token = null
      this.isAuthenticated = false
      
      localStorage.removeItem('auth_token')
      delete axios.defaults.headers.common['Authorization']
    },

    // Initialize auth state on app startup
    async initAuth() {
      if (this.token) {
        try {
          await this.fetchUser()
        } catch (error) {
          this.logout()
        }
      }
    }
  }
})
