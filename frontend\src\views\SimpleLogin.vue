<template>
  <div class="simple-login">
    <h1>家庭办公平台 - 简化登录</h1>
    <form @submit.prevent="handleSubmit">
      <div>
        <label>用户名:</label>
        <input 
          v-model="username" 
          type="text" 
          required 
          style="margin: 10px; padding: 8px; width: 200px;"
        />
      </div>
      <div>
        <label>密码:</label>
        <input 
          v-model="password" 
          type="password" 
          required 
          style="margin: 10px; padding: 8px; width: 200px;"
        />
      </div>
      <div>
        <button 
          type="submit" 
          :disabled="loading"
          style="margin: 10px; padding: 10px 20px; background: #409EFF; color: white; border: none; cursor: pointer;"
        >
          {{ loading ? '登录中...' : '登录' }}
        </button>
      </div>
    </form>
    
    <div v-if="message" :style="{ color: messageType === 'error' ? 'red' : 'green', margin: '10px' }">
      {{ message }}
    </div>
    
    <div style="margin-top: 20px;">
      <p>测试账户: lin<PERSON><PERSON> / linfanhao</p>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import axios from 'axios'

const router = useRouter()

const username = ref('linfanhao')
const password = ref('linfanhao')
const loading = ref(false)
const message = ref('')
const messageType = ref('')

const handleSubmit = async () => {
  console.log('handleSubmit called')
  loading.value = true
  message.value = ''
  
  try {
    const response = await axios.post('http://localhost:3002/api/auth/login', {
      username: username.value,
      password: password.value
    })
    
    console.log('Login response:', response.data)
    
    // 保存token到localStorage
    localStorage.setItem('auth_token', response.data.token)
    
    // 设置axios默认header
    axios.defaults.headers.common['Authorization'] = `Bearer ${response.data.token}`
    
    message.value = '登录成功！正在跳转...'
    messageType.value = 'success'
    
    // 跳转到仪表盘
    setTimeout(() => {
      router.push('/dashboard')
    }, 1000)
    
  } catch (error) {
    console.error('Login error:', error)
    message.value = error.response?.data?.error || '登录失败'
    messageType.value = 'error'
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.simple-login {
  max-width: 400px;
  margin: 50px auto;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  text-align: center;
}

.simple-login h1 {
  color: #333;
  margin-bottom: 20px;
}

.simple-login form div {
  margin: 15px 0;
}

.simple-login label {
  display: inline-block;
  width: 80px;
  text-align: right;
}
</style>
