<template>
  <div 
    ref="containerRef" 
    class="virtual-list-container"
    :style="{ height: containerHeight + 'px' }"
    @scroll="handleScroll"
  >
    <div 
      class="virtual-list-phantom" 
      :style="{ height: totalHeight + 'px' }"
    ></div>
    
    <div 
      class="virtual-list-content"
      :style="{ transform: `translateY(${offsetY}px)` }"
    >
      <div
        v-for="item in visibleItems"
        :key="getItemKey(item)"
        class="virtual-list-item"
        :style="{ height: itemHeight + 'px' }"
      >
        <slot :item="item" :index="item.index" />
      </div>
    </div>
    
    <!-- 加载更多指示器 -->
    <div 
      v-if="loading" 
      class="virtual-list-loading"
    >
      <el-loading-spinner />
      <span>加载中...</span>
    </div>
    
    <!-- 无更多数据提示 -->
    <div 
      v-if="!hasMore && items.length > 0" 
      class="virtual-list-no-more"
    >
      没有更多数据了
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'

const props = defineProps({
  items: {
    type: Array,
    default: () => []
  },
  itemHeight: {
    type: Number,
    default: 60
  },
  containerHeight: {
    type: Number,
    default: 400
  },
  buffer: {
    type: Number,
    default: 5
  },
  loading: {
    type: Boolean,
    default: false
  },
  hasMore: {
    type: Boolean,
    default: true
  },
  keyField: {
    type: String,
    default: 'id'
  }
})

const emit = defineEmits(['loadMore', 'scroll'])

// 响应式数据
const containerRef = ref(null)
const scrollTop = ref(0)

// 计算属性
const totalHeight = computed(() => props.items.length * props.itemHeight)

const visibleCount = computed(() => Math.ceil(props.containerHeight / props.itemHeight))

const startIndex = computed(() => {
  const index = Math.floor(scrollTop.value / props.itemHeight)
  return Math.max(0, index - props.buffer)
})

const endIndex = computed(() => {
  const index = startIndex.value + visibleCount.value + props.buffer * 2
  return Math.min(props.items.length, index)
})

const visibleItems = computed(() => {
  return props.items.slice(startIndex.value, endIndex.value).map((item, index) => ({
    ...item,
    index: startIndex.value + index
  }))
})

const offsetY = computed(() => startIndex.value * props.itemHeight)

// 方法
const getItemKey = (item) => {
  return item[props.keyField] || item.index
}

const handleScroll = (event) => {
  const { scrollTop: newScrollTop, scrollHeight, clientHeight } = event.target
  scrollTop.value = newScrollTop
  
  emit('scroll', {
    scrollTop: newScrollTop,
    scrollHeight,
    clientHeight
  })
  
  // 检查是否需要加载更多
  if (props.hasMore && !props.loading) {
    const threshold = 100 // 距离底部100px时开始加载
    if (scrollHeight - newScrollTop - clientHeight < threshold) {
      emit('loadMore')
    }
  }
}

// 滚动到指定项
const scrollToItem = (index) => {
  if (containerRef.value) {
    const targetScrollTop = index * props.itemHeight
    containerRef.value.scrollTop = targetScrollTop
  }
}

// 滚动到顶部
const scrollToTop = () => {
  if (containerRef.value) {
    containerRef.value.scrollTop = 0
  }
}

// 滚动到底部
const scrollToBottom = () => {
  if (containerRef.value) {
    containerRef.value.scrollTop = containerRef.value.scrollHeight
  }
}

// 监听items变化，保持滚动位置
watch(() => props.items.length, (newLength, oldLength) => {
  if (newLength > oldLength && containerRef.value) {
    // 新增数据时，如果用户在底部附近，自动滚动到新内容
    const { scrollTop, scrollHeight, clientHeight } = containerRef.value
    const isNearBottom = scrollHeight - scrollTop - clientHeight < 200
    
    if (isNearBottom) {
      nextTick(() => {
        scrollToBottom()
      })
    }
  }
})

// 暴露方法给父组件
defineExpose({
  scrollToItem,
  scrollToTop,
  scrollToBottom,
  getScrollTop: () => scrollTop.value
})

onMounted(() => {
  // 初始化滚动位置
  if (containerRef.value) {
    scrollTop.value = containerRef.value.scrollTop
  }
})
</script>

<style scoped>
.virtual-list-container {
  position: relative;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.virtual-list-phantom {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: -1;
}

.virtual-list-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

.virtual-list-item {
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  padding: 0 16px;
}

.virtual-list-item:last-child {
  border-bottom: none;
}

.virtual-list-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #909399;
  font-size: 14px;
  gap: 8px;
}

.virtual-list-no-more {
  text-align: center;
  padding: 20px;
  color: #c0c4cc;
  font-size: 14px;
  border-top: 1px solid #f0f0f0;
}

/* 滚动条样式 */
.virtual-list-container::-webkit-scrollbar {
  width: 6px;
}

.virtual-list-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.virtual-list-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.virtual-list-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
