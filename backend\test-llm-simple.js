const http = require('http');

// 测试数据
const postData = JSON.stringify({
  text: '我想在家里安装一个新的空调系统',
  type: 'description'
});

// 首先获取token
const loginData = JSON.stringify({
  username: 'admin',
  password: 'admin123'
});

console.log('正在获取认证token...');

const loginOptions = {
  hostname: 'localhost',
  port: 3000,
  path: '/api/auth/login',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': Buffer.byteLength(loginData)
  }
};

const loginReq = http.request(loginOptions, (res) => {
  let data = '';
  
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    try {
      const response = JSON.parse(data);
      if (response.token) {
        console.log('获取token成功');
        
        // 测试LLM API
        const llmOptions = {
          hostname: 'localhost',
          port: 3000,
          path: '/api/llm/expand',
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${response.token}`,
            'Content-Length': Buffer.byteLength(postData)
          }
        };
        
        console.log('正在测试LLM API...');
        
        const llmReq = http.request(llmOptions, (res) => {
          let llmData = '';
          
          res.on('data', (chunk) => {
            llmData += chunk;
          });
          
          res.on('end', () => {
            console.log('LLM API响应状态:', res.statusCode);
            console.log('LLM API响应数据:', llmData);
            
            if (res.statusCode === 200) {
              try {
                const llmResponse = JSON.parse(llmData);
                console.log('✅ LLM API测试成功');
                console.log('原文:', llmResponse.originalText);
                console.log('扩展结果:', llmResponse.expandedText.substring(0, 200) + '...');
              } catch (e) {
                console.log('❌ 解析LLM响应JSON失败:', e.message);
              }
            } else {
              console.log('❌ LLM API测试失败');
            }
          });
        });
        
        llmReq.on('error', (e) => {
          console.error('❌ LLM API请求错误:', e.message);
        });
        
        llmReq.write(postData);
        llmReq.end();
        
      } else {
        console.log('❌ 获取token失败:', response);
      }
    } catch (e) {
      console.log('❌ 解析登录响应失败:', e.message);
      console.log('原始响应:', data);
    }
  });
});

loginReq.on('error', (e) => {
  console.error('❌ 登录请求错误:', e.message);
});

loginReq.write(loginData);
loginReq.end();
