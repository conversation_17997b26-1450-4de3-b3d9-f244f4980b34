const { pool } = require('./config/database');

async function checkMySQLVersion() {
  try {
    const [version] = await pool.execute('SELECT VERSION() as version');
    console.log('MySQL Version:', version[0].version);
    
    // Check if server uses prepared statements correctly
    const [test] = await pool.execute('SELECT ? as test', [123]);
    console.log('Parameter test:', test[0].test);
    
    // Test LIMIT with different approaches
    console.log('\nTesting LIMIT approaches:');
    
    // Approach 1: Using query instead of execute
    const [result1] = await pool.query('SELECT COUNT(*) as count FROM proposals LIMIT 10');
    console.log('query() method works:', result1[0].count);
    
    // Approach 2: Using integer parameters
    try {
      const [result2] = await pool.execute('SELECT COUNT(*) as count FROM proposals LIMIT ?', [10]);
      console.log('execute() with LIMIT works:', result2[0].count);
    } catch (e) {
      console.log('execute() with LIMIT failed:', e.message);
    }
    
  } catch (error) {
    console.error('Version check failed:', error);
  } finally {
    process.exit(0);
  }
}

checkMySQLVersion();
