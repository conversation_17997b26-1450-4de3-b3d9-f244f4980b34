@echo off
chcp 65001 >nul
title FamilyOAPlatform 服务管理器

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                🚀 FamilyOAPlatform 服务启动器                  ║
echo ║                                                              ║
echo ║  正在启动以下服务：                                             ║
echo ║  • MySQL 数据库服务                                           ║
echo ║  • 后端API服务 (端口: 3000)                                   ║
echo ║  • 前端Web服务 (端口: 5173)                                   ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:: 设置日志文件
set LOG_FILE=logs\start-%date:~0,4%%date:~5,2%%date:~8,2%-%time:~0,2%%time:~3,2%%time:~6,2%.log
if not exist "logs" mkdir logs
echo 服务启动时间: %date% %time% > "%LOG_FILE%"

:: 检查配置文件
echo [检查] 验证配置文件...
if not exist "backend\.env" (
    echo ❌ 错误：后端配置文件不存在
    echo.
    echo 请先运行 install.bat 完成安装
    echo 后端配置文件不存在 >> "%LOG_FILE%"
    pause
    exit /b 1
)

:: 检查Google AI API密钥配置
findstr /C:"your_google_ai_api_key_here" backend\.env >nul
if %errorLevel% equ 0 (
    echo ⚠️  警告：Google AI API密钥未配置
    echo.
    echo AI功能将无法使用，建议运行 config-wizard.bat 进行配置
    echo 是否继续启动？(Y/N)
    set /p continue="请选择: "
    if /i not "%continue%"=="Y" (
        echo 用户取消启动 >> "%LOG_FILE%"
        exit /b 0
    )
    echo AI API密钥未配置，用户选择继续 >> "%LOG_FILE%"
)

echo ✅ 配置文件检查完成
echo.

:: 启动MySQL数据库
echo [1/3] 启动MySQL数据库服务...
echo [1/3] 启动MySQL数据库服务... >> "%LOG_FILE%"

:: 检查MySQL是否已在运行
tasklist /FI "IMAGENAME eq mysqld.exe" 2>NUL | find /I /N "mysqld.exe">NUL
if %errorLevel% equ 0 (
    echo ✅ MySQL服务已在运行
    echo MySQL服务已在运行 >> "%LOG_FILE%"
) else (
    if exist "mysql\bin\mysqld.exe" (
        echo 正在启动MySQL服务...
        start /b mysql\bin\mysqld --defaults-file=mysql\my.ini >> "%LOG_FILE%" 2>&1
        
        :: 等待MySQL启动
        echo 等待MySQL服务启动...
        timeout /t 8 /nobreak >nul
        
        :: 验证MySQL是否启动成功
        mysql\bin\mysql -u root --execute="SELECT 1;" >nul 2>&1
        if %errorLevel% equ 0 (
            echo ✅ MySQL服务启动成功
            echo MySQL服务启动成功 >> "%LOG_FILE%"
        ) else (
            echo ❌ MySQL服务启动失败
            echo MySQL服务启动失败 >> "%LOG_FILE%"
            echo.
            echo 请检查：
            echo 1. 端口3306是否被占用
            echo 2. mysql\data目录是否存在
            echo 3. 查看日志文件获取详细错误信息
            pause
            exit /b 1
        )
    ) else (
        echo ❌ 错误：MySQL未安装，请先运行 install.bat
        echo MySQL未安装 >> "%LOG_FILE%"
        pause
        exit /b 1
    )
)

echo.

:: 启动后端服务
echo [2/3] 启动后端API服务...
echo [2/3] 启动后端API服务... >> "%LOG_FILE%"

:: 检查端口3000是否被占用
netstat -an | find ":3000" >nul
if %errorLevel% equ 0 (
    echo ⚠️  警告：端口3000已被占用
    echo 端口3000已被占用 >> "%LOG_FILE%"
    
    echo 是否要结束占用进程？(Y/N)
    set /p kill_process="请选择: "
    if /i "%kill_process%"=="Y" (
        for /f "tokens=5" %%a in ('netstat -ano ^| find ":3000"') do (
            taskkill /PID %%a /F >nul 2>&1
        )
        echo 已结束占用进程
        timeout /t 2 /nobreak >nul
    )
)

:: 启动后端服务
echo 正在启动后端服务...
cd backend
start "FamilyOA-Backend" cmd /k "echo 🔧 后端API服务 & echo 端口: 3000 & echo 日志: ../logs/backend.log & echo. & npm start"
cd ..

echo ✅ 后端服务启动中...
echo 后端服务启动命令已执行 >> "%LOG_FILE%"

:: 等待后端服务启动
echo 等待后端服务就绪...
set /a count=0
:wait_backend
timeout /t 2 /nobreak >nul
curl -s http://localhost:3000/health >nul 2>&1
if %errorLevel% equ 0 (
    echo ✅ 后端服务就绪
    echo 后端服务就绪 >> "%LOG_FILE%"
    goto :backend_ready
)

set /a count+=1
if %count% lss 15 (
    echo 等待中... (%count%/15)
    goto :wait_backend
)

echo ⚠️  后端服务启动超时，但将继续启动前端
echo 后端服务启动超时 >> "%LOG_FILE%"

:backend_ready
echo.

:: 启动前端服务
echo [3/3] 启动前端Web服务...
echo [3/3] 启动前端Web服务... >> "%LOG_FILE%"

:: 检查端口5173是否被占用
netstat -an | find ":5173" >nul
if %errorLevel% equ 0 (
    echo ⚠️  警告：端口5173已被占用
    echo 端口5173已被占用 >> "%LOG_FILE%"
    
    echo 是否要结束占用进程？(Y/N)
    set /p kill_process="请选择: "
    if /i "%kill_process%"=="Y" (
        for /f "tokens=5" %%a in ('netstat -ano ^| find ":5173"') do (
            taskkill /PID %%a /F >nul 2>&1
        )
        echo 已结束占用进程
        timeout /t 2 /nobreak >nul
    )
)

:: 启动前端服务
echo 正在启动前端服务...
cd frontend
start "FamilyOA-Frontend" cmd /k "echo 🌐 前端Web服务 & echo 端口: 5173 & echo 访问: http://localhost:5173 & echo. & npm run dev"
cd ..

echo ✅ 前端服务启动中...
echo 前端服务启动命令已执行 >> "%LOG_FILE%"

:: 等待前端服务启动
echo 等待前端服务就绪...
set /a count=0
:wait_frontend
timeout /t 3 /nobreak >nul
curl -s http://localhost:5173 >nul 2>&1
if %errorLevel% equ 0 (
    echo ✅ 前端服务就绪
    echo 前端服务就绪 >> "%LOG_FILE%"
    goto :frontend_ready
)

set /a count+=1
if %count% lss 20 (
    echo 等待中... (%count%/20)
    goto :wait_frontend
)

echo ⚠️  前端服务启动超时，请手动检查
echo 前端服务启动超时 >> "%LOG_FILE%"

:frontend_ready
echo.

:: 显示启动完成信息
echo 服务启动完成时间: %date% %time% >> "%LOG_FILE%"

echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🎉 服务启动完成！                           ║
echo ║                                                              ║
echo ║  服务状态：                                                    ║
echo ║  📊 MySQL数据库: 运行中                                        ║
echo ║  🔧 后端API服务: http://localhost:3000                        ║
echo ║  🌐 前端Web服务: http://localhost:5173                        ║
echo ║                                                              ║
echo ║  快速测试：                                                    ║
echo ║  • 健康检查: curl http://localhost:3000/health                ║
echo ║  • 前端页面: http://localhost:5173                            ║
echo ║                                                              ║
echo ║  管理命令：                                                    ║
echo ║  • 停止服务: stop.bat                                         ║
echo ║  • 重启服务: restart.bat                                      ║
echo ║  • 查看日志: logs\                                            ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:: 询问是否打开浏览器
echo 是否要打开浏览器访问应用？(Y/N)
set /p open_browser="请选择: "
if /i "%open_browser%"=="Y" (
    echo 正在打开浏览器...
    timeout /t 3 /nobreak >nul
    start http://localhost:5173
    echo 浏览器已打开 >> "%LOG_FILE%"
)

echo.
echo 💡 提示：
echo   - 保持此窗口打开以查看服务状态
echo   - 按Ctrl+C可以停止服务
echo   - 服务日志保存在 logs\ 目录中
echo.

:: 显示实时状态监控
echo ═══════════════ 服务监控 ═══════════════
echo 按任意键退出监控...
echo.

:monitor_loop
:: 检查服务状态
set backend_status=❌
set frontend_status=❌
set mysql_status=❌

:: 检查MySQL
tasklist /FI "IMAGENAME eq mysqld.exe" 2>NUL | find /I /N "mysqld.exe">NUL
if %errorLevel% equ 0 set mysql_status=✅

:: 检查后端
curl -s http://localhost:3000/health >nul 2>&1
if %errorLevel% equ 0 set backend_status=✅

:: 检查前端
curl -s http://localhost:5173 >nul 2>&1
if %errorLevel% equ 0 set frontend_status=✅

:: 显示状态
echo [%time%] MySQL: %mysql_status%  后端: %backend_status%  前端: %frontend_status%

:: 检查是否有按键
timeout /t 5 /nobreak >nul
if %errorLevel% equ 0 goto :end_monitor

goto :monitor_loop

:end_monitor
echo.
echo 监控已停止，服务继续在后台运行
echo 如需停止服务，请运行 stop.bat
echo.
pause
