import axios from 'axios'
import { useAuthStore } from '../stores/auth'
import { ElMessage } from 'element-plus'

// 创建axios实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3002/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 创建AI专用的axios实例，使用更长的超时时间
const aiApi = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3002/api',
  timeout: 60000, // AI请求超时时间设为60秒
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器 - 添加token
const requestInterceptor = (config) => {
  const authStore = useAuthStore()
  if (authStore.token) {
    config.headers.Authorization = `Bearer ${authStore.token}`
  }
  return config
}

const requestErrorInterceptor = (error) => {
  return Promise.reject(error)
}

// 为普通API添加拦截器
api.interceptors.request.use(requestInterceptor, requestErrorInterceptor)

// 为AI API添加拦截器
aiApi.interceptors.request.use(requestInterceptor, requestErrorInterceptor)

// 响应拦截器 - 处理错误和token过期
const responseSuccessInterceptor = (response) => {
  // 记录成功的API调用（开发环境）
  if (import.meta.env.DEV) {
    console.log(`API Success: ${response.config.method?.toUpperCase()} ${response.config.url}`, {
      status: response.status,
      data: response.data
    })
  }
  return response
}

const responseErrorInterceptor = (error) => {
  const authStore = useAuthStore()

  // 记录API错误（开发环境）
  if (import.meta.env.DEV) {
    console.error(`API Error: ${error.config?.method?.toUpperCase()} ${error.config?.url}`, {
      status: error.response?.status,
      data: error.response?.data,
      message: error.message
    })
  }

  if (error.response) {
    const { status, data } = error.response

    // Token过期或无效
    if (status === 401) {
      authStore.logout()

      // 根据错误代码显示不同消息
      const message = data.code === 'TOKEN_EXPIRED'
        ? '登录已过期，请重新登录'
        : '身份验证失败，请重新登录'

      ElMessage.error(message)

      // 如果不在登录页面，跳转到登录页面
      if (window.location.pathname !== '/login') {
        window.location.href = '/login'
      }
      return Promise.reject(error)
    }

    // 权限不足
    if (status === 403) {
      ElMessage.error(data.error || '权限不足，无法执行此操作')
      return Promise.reject(error)
    }

    // 请求过于频繁
    if (status === 429) {
      const retryAfter = data.retryAfter || 60
      ElMessage.error(`请求过于频繁，请${retryAfter}秒后重试`)
      return Promise.reject(error)
    }

    // 服务器错误
    if (status >= 500) {
      const message = status === 503
        ? '服务暂时不可用，请稍后重试'
        : '服务器内部错误，请稍后重试'
      ElMessage.error(message)
      return Promise.reject(error)
    }

    // 其他客户端错误
    if (status >= 400) {
      // 显示具体的错误信息
      const message = data.error || data.message || '请求失败'
      ElMessage.error(message)

      // 如果有详细错误信息，在控制台显示
      if (data.details && import.meta.env.DEV) {
        console.error('Validation errors:', data.details)
      }

      return Promise.reject(error)
    }
  } else if (error.request) {
    // 网络错误
    if (error.code === 'ECONNABORTED') {
      ElMessage.error('请求超时，请检查网络连接')
    } else if (error.code === 'ERR_NETWORK') {
      ElMessage.error('网络连接失败，请检查网络设置')
    } else {
      ElMessage.error('网络错误，请稍后重试')
    }
    return Promise.reject(error)
  } else {
    // 其他错误
    ElMessage.error('请求配置错误，请联系技术支持')
    return Promise.reject(error)
  }

  return Promise.reject(error)
}

// 为普通API添加响应拦截器
api.interceptors.response.use(responseSuccessInterceptor, responseErrorInterceptor)

// 为AI API添加响应拦截器
aiApi.interceptors.response.use(responseSuccessInterceptor, responseErrorInterceptor)

export default api
export { aiApi }
