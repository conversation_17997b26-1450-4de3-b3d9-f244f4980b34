/* 增强主题配置 */

/* 自定义CSS变量 */
:root {
  /* 主色调 - 现代蓝紫渐变 */
  --primary-color: #667eea;
  --primary-light: #764ba2;
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  
  /* 成功色 - 清新绿色 */
  --success-color: #48bb78;
  --success-light: #68d391;
  --success-gradient: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
  
  /* 警告色 - 温暖橙色 */
  --warning-color: #ed8936;
  --warning-light: #f6ad55;
  --warning-gradient: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
  
  /* 危险色 - 柔和红色 */
  --danger-color: #f56565;
  --danger-light: #fc8181;
  --danger-gradient: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
  
  /* 信息色 - 优雅灰色 */
  --info-color: #4299e1;
  --info-light: #63b3ed;
  --info-gradient: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
  
  /* 文字颜色 */
  --text-primary: #2d3748;
  --text-secondary: #4a5568;
  --text-tertiary: #718096;
  --text-placeholder: #a0aec0;
  --text-disabled: #cbd5e0;
  
  /* 背景色 */
  --bg-primary: #ffffff;
  --bg-secondary: #f7fafc;
  --bg-tertiary: #edf2f7;
  --bg-overlay: rgba(0, 0, 0, 0.6);
  --bg-glass: rgba(255, 255, 255, 0.8);
  
  /* 边框色 */
  --border-color: #e2e8f0;
  --border-light: #edf2f7;
  --border-dark: #cbd5e0;
  
  /* 阴影 */
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-glow: 0 0 20px rgba(102, 126, 234, 0.3);
  
  /* 圆角 */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-full: 9999px;
  
  /* 间距 */
  --space-1: 4px;
  --space-2: 8px;
  --space-3: 12px;
  --space-4: 16px;
  --space-5: 20px;
  --space-6: 24px;
  --space-8: 32px;
  --space-10: 40px;
  --space-12: 48px;
  --space-16: 64px;
  
  /* 字体大小 */
  --text-xs: 12px;
  --text-sm: 14px;
  --text-base: 16px;
  --text-lg: 18px;
  --text-xl: 20px;
  --text-2xl: 24px;
  --text-3xl: 30px;
  --text-4xl: 36px;
  
  /* 过渡动画 */
  --transition-fast: 150ms ease-in-out;
  --transition-base: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
  --transition-bounce: 400ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* 深色主题 */
@media (prefers-color-scheme: dark) {
  :root {
    --text-primary: #f7fafc;
    --text-secondary: #e2e8f0;
    --text-tertiary: #cbd5e0;
    --text-placeholder: #718096;
    --text-disabled: #4a5568;
    
    --bg-primary: #1a202c;
    --bg-secondary: #2d3748;
    --bg-tertiary: #4a5568;
    --bg-overlay: rgba(0, 0, 0, 0.8);
    --bg-glass: rgba(26, 32, 44, 0.8);
    
    --border-color: #4a5568;
    --border-light: #2d3748;
    --border-dark: #718096;
  }
}

/* 玻璃态效果 */
.glass-effect {
  background: var(--bg-glass);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 渐变背景 */
.gradient-bg {
  background: var(--primary-gradient);
}

.gradient-success {
  background: var(--success-gradient);
}

.gradient-warning {
  background: var(--warning-gradient);
}

.gradient-danger {
  background: var(--danger-gradient);
}

.gradient-info {
  background: var(--info-gradient);
}

/* 悬浮效果 */
.hover-lift {
  transition: transform var(--transition-base), box-shadow var(--transition-base);
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* 脉冲动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 淡入动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out;
}

/* 滑入动画 */
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

/* 缩放动画 */
@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-scale-in {
  animation: scaleIn 0.2s ease-out;
}

/* 加载骨架屏 */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* 自定义Element Plus组件样式 */
.el-card.modern-card {
  border: none;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  transition: var(--transition-base);
  overflow: hidden;
}

.el-card.modern-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-1px);
}

.el-button.modern-btn {
  border-radius: var(--radius-md);
  font-weight: 500;
  transition: var(--transition-base);
  border: none;
  position: relative;
  overflow: hidden;
}

.el-button.modern-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.el-button.modern-btn:hover::before {
  left: 100%;
}

/* 按钮文字颜色优化 - 确保紫色背景按钮文字为白色 */
.el-button--primary {
  color: #ffffff !important;
}

.el-button--primary:hover,
.el-button--primary:focus,
.el-button--primary:active {
  color: #ffffff !important;
}

.el-button--primary.is-disabled {
  color: #ffffff !important;
}

/* 其他紫色背景按钮的文字颜色 */
.el-button[style*="background"],
.el-button[style*="667eea"],
.el-button[style*="764ba2"] {
  color: #ffffff !important;
}

.el-button[style*="background"]:hover,
.el-button[style*="667eea"]:hover,
.el-button[style*="764ba2"]:hover {
  color: #ffffff !important;
}

.el-input.modern-input .el-input__inner {
  border-radius: var(--radius-md);
  border: 2px solid var(--border-color);
  transition: var(--transition-base);
  padding: var(--space-3) var(--space-4);
}

.el-input.modern-input .el-input__inner:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 状态指示器 */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-sm);
  font-weight: 500;
}

.status-indicator::before {
  content: '';
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
}

/* 响应式工具类 */
@media (max-width: 640px) {
  .sm\:hidden {
    display: none;
  }
  
  .sm\:block {
    display: block;
  }
  
  .sm\:text-sm {
    font-size: var(--text-sm);
  }
}

@media (max-width: 768px) {
  .md\:hidden {
    display: none;
  }
  
  .md\:block {
    display: block;
  }
}

@media (max-width: 1024px) {
  .lg\:hidden {
    display: none;
  }
  
  .lg\:block {
    display: block;
  }
}
