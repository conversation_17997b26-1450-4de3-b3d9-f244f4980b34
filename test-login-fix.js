const axios = require('axios');

async function testLoginFix() {
  try {
    console.log('测试修复后的登录功能...');
    
    // 测试后端API是否正常
    console.log('1. 测试后端API...');
    const response = await axios.post('http://localhost:3002/api/auth/login', {
      username: 'lin<PERSON><PERSON>',
      password: 'lin<PERSON><PERSON>'
    });
    
    console.log('✅ 后端API正常工作');
    console.log('Token:', response.data.token.substring(0, 20) + '...');
    console.log('用户:', response.data.user.username);
    
    // 测试获取用户信息
    console.log('2. 测试获取用户信息...');
    const userResponse = await axios.get('http://localhost:3002/api/auth/me', {
      headers: {
        'Authorization': `Bearer ${response.data.token}`
      }
    });
    
    console.log('✅ 用户信息获取正常');
    console.log('用户详情:', userResponse.data.user.real_name);
    
    console.log('\n🎉 后端登录功能完全正常！');
    console.log('📝 前端修复内容:');
    console.log('   - 修复了.env.development中的API端口配置');
    console.log('   - 修复了Profile.vue中硬编码的端口');
    console.log('   - 修复了Login.vue中的alert函数问题');
    console.log('   - 恢复了正常的登录表单验证流程');
    console.log('\n🌐 请在浏览器中访问 http://localhost:5173/login 测试登录');
    console.log('🔑 用户名: linfanhao');
    console.log('🔑 密码: linfanhao');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
  }
}

testLoginFix();
