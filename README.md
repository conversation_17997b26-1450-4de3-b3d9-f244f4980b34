# 家庭办公平台 (Family OA Platform)

一个专为家庭设计的高效沟通和决策管理平台，让家庭讨论更加清晰和有序。

## 🌟 特色功能

### 核心功能
- **提案工作流**: 家庭成员可以提出需求、提供解决方案并记录最终结果
- **AI内容扩展**: 集成Google Gemini AI，自动扩展和完善用户提交的内容
- **AI中立仲裁**: AI作为中立第三方评估提案，提供客观意见和量化评分

### 技术特色
- **现代化技术栈**: Vue.js 3 + Node.js + MySQL + Google Gemini AI
- **响应式设计**: 支持桌面和移动设备，采用扁平化极简设计
- **多用户并发**: 支持家庭多成员同时使用
- **中文界面**: 完全中文化的用户界面

## 🛠️ 技术栈

### 前端 (Frontend)
- **Vue.js 3**: 现代化前端框架
- **Element Plus**: 高质量的Vue.js组件库
- **Pinia**: 状态管理
- **Vue Router**: 路由管理
- **Vite**: 构建工具
- **Axios**: HTTP客户端

### 后端 (Backend)
- **Node.js**: 服务器运行环境
- **Express.js**: Web应用框架
- **MySQL**: 关系型数据库
- **Google Gemini AI**: AI内容处理
- **JWT**: 身份验证
- **bcryptjs**: 密码加密

### 开发工具
- **ESLint**: 代码规范检查
- **Prettier**: 代码格式化
- **Nodemon**: 开发环境热重载

## 📦 项目结构

```
FamilyOAPlatform/
├── backend/                 # 后端服务
│   ├── config/             # 配置文件
│   ├── routes/             # API路由
│   ├── server.js           # 服务器入口
│   ├── package.json        # 后端依赖
│   └── .env.example        # 环境变量示例
├── frontend/               # 前端应用
│   ├── src/               # 源代码
│   │   ├── components/    # 公共组件
│   │   ├── views/         # 页面视图
│   │   ├── stores/        # 状态管理
│   │   ├── router/        # 路由配置
│   │   └── main.js        # 应用入口
│   ├── package.json       # 前端依赖
│   └── vite.config.js     # Vite配置
└── README.md              # 项目说明
```

## 🚀 快速开始

### 环境要求
- Node.js >= 16.0.0
- MySQL >= 8.0
- Google AI Studio API密钥

### 1. 克隆项目
```bash
git clone <your-repository-url>
cd FamilyOAPlatform
```

### 2. 配置数据库
```sql
CREATE DATABASE family_oa_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 3. 配置后端
```bash
cd backend
npm install
cp .env.example .env
# 编辑 .env 文件，填入数据库和API配置
npm run dev
```

### 4. 配置前端
```bash
cd frontend
npm install
npm run dev
```

### 5. 访问应用
- 前端: http://localhost:5173
- 后端API: http://localhost:3000

## ⚙️ 环境配置

### 后端环境变量 (.env)
```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=family_oa_platform
DB_USER=root
DB_PASSWORD=your_password

# 服务器配置
PORT=3000
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=7d

# Google AI配置
GOOGLE_AI_API_KEY=your_google_ai_api_key
GOOGLE_AI_MODEL=gemini-2.0-flash-exp

# 前端URL
FRONTEND_URL=http://localhost:5173
```

## 📱 功能截图

### 仪表盘
- 概览统计信息
- 最近提案列表
- 快速操作入口

### 提案管理
- 创建和编辑提案
- 状态跟踪
- 评论和投票系统

### AI功能
- 内容智能扩展
- 中立仲裁评分
- 客观建议提供

## 🔒 安全特性

- JWT身份验证
- 密码加密存储
- SQL注入防护
- XSS攻击防护
- CORS跨域保护
- 请求频率限制

## 🎯 使用场景

- **家庭决策**: 大到搬家买房，小到周末活动安排
- **资源分配**: 家庭预算分配、家务分工安排
- **规则制定**: 家庭公约、作息时间安排
- **活动策划**: 节假日安排、家庭旅行计划

## 📈 开发计划

- [ ] 移动端APP
- [ ] 消息推送通知
- [ ] 文件附件支持
- [ ] 数据分析报表
- [ ] 多语言支持
- [ ] 第三方集成

## 🤝 贡献指南

1. Fork 本仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🆘 支持

如果您遇到任何问题或有建议，请通过以下方式联系：

- 提交 [Issue](issues)
- 发送邮件到 <EMAIL>
- 查看 [Wiki](wiki) 获取更多帮助

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和用户。

---

**让家庭沟通更高效，让决策更透明！** 🏠✨
