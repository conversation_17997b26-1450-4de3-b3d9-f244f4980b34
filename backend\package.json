{"name": "family-oa-platform-backend", "version": "1.0.0", "description": "Backend for Family OA Platform - making family communication more efficient", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "keywords": ["family", "communication", "proposal", "workflow", "llm"], "author": "Your Name", "license": "MIT", "dependencies": {"@google/generative-ai": "^0.2.1", "axios": "^1.10.0", "bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.2.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mysql2": "^3.6.5"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2"}}