const rateLimit = require('express-rate-limit');
const helmet = require('helmet');

// 通用限流配置
const createRateLimit = (windowMs, max, message) => {
  return rateLimit({
    windowMs,
    max,
    message: { error: message },
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req, res) => {
      res.status(429).json({
        error: message,
        retryAfter: Math.ceil(windowMs / 1000)
      });
    }
  });
};

// 严格限流 - 用于敏感操作
const strictRateLimit = createRateLimit(
  15 * 60 * 1000, // 15分钟
  5, // 最多5次请求
  '请求过于频繁，请稍后再试'
);

// 认证限流 - 用于登录注册
const authRateLimit = createRateLimit(
  15 * 60 * 1000, // 15分钟
  10, // 最多10次请求
  '登录尝试过于频繁，请15分钟后再试'
);

// API限流 - 用于一般API
const apiRateLimit = createRateLimit(
  15 * 60 * 1000, // 15分钟
  100, // 最多100次请求
  'API请求过于频繁，请稍后再试'
);

// LLM API限流 - 用于AI功能
const llmRateLimit = createRateLimit(
  60 * 1000, // 1分钟
  10, // 最多10次请求
  'AI功能使用过于频繁，请稍后再试'
);

// 安全头配置
const securityHeaders = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'", "https://generativelanguage.googleapis.com"],
      frameSrc: ["'none'"],
      objectSrc: ["'none'"],
      upgradeInsecureRequests: [],
    },
  },
  crossOriginEmbedderPolicy: false, // 避免与某些第三方服务冲突
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
});

// 请求大小限制中间件
const requestSizeLimit = (req, res, next) => {
  const contentLength = parseInt(req.get('Content-Length') || '0');
  const maxSize = 10 * 1024 * 1024; // 10MB
  
  if (contentLength > maxSize) {
    return res.status(413).json({
      error: '请求体过大',
      maxSize: '10MB',
      currentSize: `${Math.round(contentLength / 1024 / 1024 * 100) / 100}MB`
    });
  }
  
  next();
};

// IP白名单中间件（可选）
const ipWhitelist = (allowedIPs = []) => {
  return (req, res, next) => {
    if (allowedIPs.length === 0) {
      return next(); // 如果没有配置白名单，则跳过检查
    }
    
    const clientIP = req.ip || req.connection.remoteAddress || req.socket.remoteAddress;
    
    if (!allowedIPs.includes(clientIP)) {
      return res.status(403).json({
        error: '访问被拒绝',
        message: 'IP地址不在允许列表中'
      });
    }
    
    next();
  };
};

// 请求日志中间件
const requestLogger = (req, res, next) => {
  const start = Date.now();
  const originalSend = res.send;
  
  res.send = function(data) {
    const duration = Date.now() - start;
    const logData = {
      timestamp: new Date().toISOString(),
      method: req.method,
      url: req.originalUrl,
      ip: req.ip || req.connection.remoteAddress,
      userAgent: req.get('User-Agent'),
      statusCode: res.statusCode,
      duration: `${duration}ms`,
      contentLength: res.get('Content-Length') || 0
    };
    
    // 记录敏感操作
    if (req.originalUrl.includes('/auth/') || req.method === 'DELETE') {
      logData.sensitive = true;
      logData.userId = req.user?.userId || 'anonymous';
    }
    
    // 根据状态码决定日志级别
    if (res.statusCode >= 400) {
      console.error('HTTP Error:', JSON.stringify(logData));
    } else if (res.statusCode >= 300) {
      console.warn('HTTP Redirect:', JSON.stringify(logData));
    } else {
      console.log('HTTP Request:', JSON.stringify(logData));
    }
    
    originalSend.call(this, data);
  };
  
  next();
};

// CORS配置增强
const corsOptions = {
  origin: function (origin, callback) {
    const allowedOrigins = (process.env.ALLOWED_ORIGINS || 'http://localhost:5173').split(',');
    
    // 允许没有origin的请求（如移动应用）
    if (!origin) return callback(null, true);
    
    if (allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error('不允许的CORS来源'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  exposedHeaders: ['X-Total-Count', 'X-Page-Count'],
  maxAge: 86400 // 24小时
};

// 安全检查中间件
const securityCheck = (req, res, next) => {
  // 检查可疑的请求头
  const suspiciousHeaders = ['x-forwarded-for', 'x-real-ip', 'x-originating-ip'];
  const hasMultipleIPs = suspiciousHeaders.some(header => {
    const value = req.get(header);
    return value && value.includes(',');
  });
  
  if (hasMultipleIPs) {
    console.warn('Suspicious request with multiple IPs:', {
      ip: req.ip,
      headers: req.headers,
      url: req.originalUrl
    });
  }
  
  // 检查User-Agent
  const userAgent = req.get('User-Agent');
  if (!userAgent || userAgent.length < 10) {
    console.warn('Suspicious request with invalid User-Agent:', {
      userAgent,
      ip: req.ip,
      url: req.originalUrl
    });
  }
  
  next();
};

module.exports = {
  strictRateLimit,
  authRateLimit,
  apiRateLimit,
  llmRateLimit,
  securityHeaders,
  requestSizeLimit,
  ipWhitelist,
  requestLogger,
  corsOptions,
  securityCheck
};
