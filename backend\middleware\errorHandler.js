// 自定义错误类
class AppError extends Error {
  constructor(message, statusCode, code = null) {
    super(message);
    this.statusCode = statusCode;
    this.code = code;
    this.isOperational = true;
    
    Error.captureStackTrace(this, this.constructor);
  }
}

// 数据库错误处理
const handleDatabaseError = (error) => {
  console.error('Database Error:', error);
  
  // MySQL错误码处理
  switch (error.code) {
    case 'ER_DUP_ENTRY':
      return new AppError('数据已存在，请检查输入信息', 400, 'DUPLICATE_ENTRY');
    
    case 'ER_NO_REFERENCED_ROW_2':
      return new AppError('引用的数据不存在', 400, 'INVALID_REFERENCE');
    
    case 'ER_ROW_IS_REFERENCED_2':
      return new AppError('数据正在被使用，无法删除', 400, 'REFERENCED_DATA');
    
    case 'ER_DATA_TOO_LONG':
      return new AppError('输入数据过长', 400, 'DATA_TOO_LONG');
    
    case 'ER_BAD_NULL_ERROR':
      return new AppError('必填字段不能为空', 400, 'REQUIRED_FIELD');
    
    case 'ECONNREFUSED':
      return new AppError('数据库连接失败', 503, 'DATABASE_UNAVAILABLE');
    
    case 'PROTOCOL_CONNECTION_LOST':
      return new AppError('数据库连接丢失', 503, 'DATABASE_CONNECTION_LOST');
    
    default:
      return new AppError('数据库操作失败', 500, 'DATABASE_ERROR');
  }
};

// JWT错误处理
const handleJWTError = (error) => {
  console.error('JWT Error:', error);
  
  switch (error.name) {
    case 'JsonWebTokenError':
      return new AppError('无效的访问令牌', 401, 'INVALID_TOKEN');
    
    case 'TokenExpiredError':
      return new AppError('访问令牌已过期', 401, 'TOKEN_EXPIRED');
    
    case 'NotBeforeError':
      return new AppError('访问令牌尚未生效', 401, 'TOKEN_NOT_ACTIVE');
    
    default:
      return new AppError('令牌验证失败', 401, 'TOKEN_ERROR');
  }
};

// 验证错误处理
const handleValidationError = (error) => {
  console.error('Validation Error:', error);
  
  const errors = error.details || error.errors || [];
  const messages = errors.map(err => err.message || err.msg).join('; ');
  
  return new AppError(`输入验证失败: ${messages}`, 400, 'VALIDATION_ERROR');
};

// 开发环境错误响应
const sendErrorDev = (err, res) => {
  res.status(err.statusCode || 500).json({
    error: err.message,
    code: err.code,
    stack: err.stack,
    details: err.details || null,
    timestamp: new Date().toISOString()
  });
};

// 生产环境错误响应
const sendErrorProd = (err, res) => {
  // 操作性错误：发送给客户端
  if (err.isOperational) {
    res.status(err.statusCode).json({
      error: err.message,
      code: err.code,
      timestamp: new Date().toISOString()
    });
  } else {
    // 编程错误：不泄露错误详情
    console.error('Programming Error:', err);
    
    res.status(500).json({
      error: '服务器内部错误',
      code: 'INTERNAL_ERROR',
      timestamp: new Date().toISOString()
    });
  }
};

// 异步错误捕获包装器
const catchAsync = (fn) => {
  return (req, res, next) => {
    fn(req, res, next).catch(next);
  };
};

// 全局错误处理中间件
const globalErrorHandler = (err, req, res, next) => {
  err.statusCode = err.statusCode || 500;
  
  // 记录错误日志
  const errorLog = {
    timestamp: new Date().toISOString(),
    error: err.message,
    code: err.code,
    statusCode: err.statusCode,
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.userId || 'anonymous'
  };
  
  if (err.statusCode >= 500) {
    console.error('Server Error:', JSON.stringify(errorLog));
  } else {
    console.warn('Client Error:', JSON.stringify(errorLog));
  }
  
  let error = { ...err };
  error.message = err.message;
  
  // 处理特定类型的错误
  if (err.code && err.code.startsWith('ER_')) {
    error = handleDatabaseError(err);
  } else if (err.name && err.name.includes('JsonWebToken')) {
    error = handleJWTError(err);
  } else if (err.name === 'ValidationError') {
    error = handleValidationError(err);
  }
  
  // 根据环境发送错误响应
  if (process.env.NODE_ENV === 'development') {
    sendErrorDev(error, res);
  } else {
    sendErrorProd(error, res);
  }
};

// 未捕获异常处理
const handleUncaughtException = () => {
  process.on('uncaughtException', (err) => {
    console.error('Uncaught Exception:', err);
    console.error('Shutting down...');
    process.exit(1);
  });
};

// 未处理的Promise拒绝
const handleUnhandledRejection = () => {
  process.on('unhandledRejection', (err) => {
    console.error('Unhandled Rejection:', err);
    console.error('Shutting down...');
    process.exit(1);
  });
};

// 优雅关闭处理
const handleGracefulShutdown = (server) => {
  const shutdown = (signal) => {
    console.log(`Received ${signal}. Shutting down gracefully...`);
    
    server.close(() => {
      console.log('Process terminated');
      process.exit(0);
    });
    
    // 强制关闭超时
    setTimeout(() => {
      console.error('Could not close connections in time, forcefully shutting down');
      process.exit(1);
    }, 10000);
  };
  
  process.on('SIGTERM', () => shutdown('SIGTERM'));
  process.on('SIGINT', () => shutdown('SIGINT'));
};

// 404处理中间件
const notFoundHandler = (req, res, next) => {
  const error = new AppError(`路由 ${req.originalUrl} 不存在`, 404, 'ROUTE_NOT_FOUND');
  next(error);
};

module.exports = {
  AppError,
  catchAsync,
  globalErrorHandler,
  notFoundHandler,
  handleUncaughtException,
  handleUnhandledRejection,
  handleGracefulShutdown
};
